"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/GroupContext */ \"(app-pages-browser)/./src/contexts/GroupContext.tsx\");\n/* harmony import */ var _components_groups_GroupList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/groups/GroupList */ \"(app-pages-browser)/./src/components/groups/GroupList.tsx\");\n/* harmony import */ var _components_groups_GroupDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/groups/GroupDetail */ \"(app-pages-browser)/./src/components/groups/GroupDetail.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, loading, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null // Will redirect to auth\n        ;\n    }\n    const handleLogout = async ()=>{\n        await logout();\n        router.push('/auth');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_GroupContext__WEBPACK_IMPORTED_MODULE_4__.GroupProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"MyBinder\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"Welcome, \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: user.name || user.username\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 28\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_groups_GroupList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_groups_GroupDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"JjlT7r8cdmwnjySuxpKGq9HDRxs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQ1U7QUFDSztBQUNPO0FBQ0Y7QUFDSTtBQUUxQyxTQUFTTTs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFLEdBQUdQLDhEQUFPQTtJQUN6QyxNQUFNUSxTQUFTVCwwREFBU0E7SUFFeEJELGdEQUFTQTttQ0FBQztZQUNSLElBQUksQ0FBQ1EsV0FBVyxDQUFDRCxNQUFNO2dCQUNyQkcsT0FBT0MsSUFBSSxDQUFDO1lBQ2Q7UUFDRjtrQ0FBRztRQUFDSjtRQUFNQztRQUFTRTtLQUFPO0lBRTFCLElBQUlGLFNBQVM7UUFDWCxxQkFDRSw4REFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTFDO0lBRUEsSUFBSSxDQUFDTixNQUFNO1FBQ1QsT0FBTyxLQUFLLHdCQUF3Qjs7SUFDdEM7SUFFQSxNQUFNUSxlQUFlO1FBQ25CLE1BQU1OO1FBQ05DLE9BQU9DLElBQUksQ0FBQztJQUNkO0lBRUEscUJBQ0UsOERBQUNSLGlFQUFhQTtrQkFDWiw0RUFBQ1M7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNHO29CQUFPSCxXQUFVOzhCQUNoQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNJO3dDQUFHSixXQUFVO2tEQUFtQzs7Ozs7Ozs7Ozs7OENBRW5ELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOztnREFBd0I7OERBQzVCLDhEQUFDSztvREFBS0wsV0FBVTs4REFBZU4sS0FBS1ksSUFBSSxJQUFJWixLQUFLYSxRQUFROzs7Ozs7Ozs7Ozs7c0RBRXBFLDhEQUFDQzs0Q0FDQ0MsU0FBU1A7NENBQ1RGLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBU1QsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1Qsb0VBQVNBOzs7OztzQ0FDViw4REFBQ0Msc0VBQVdBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3RCO0dBL0R3QkM7O1FBQ1lKLDBEQUFPQTtRQUMxQkQsc0RBQVNBOzs7S0FGRksiLCJzb3VyY2VzIjpbIi9ob21lL3J5YW4vbXliaW5kZXIvc3JjL2FwcC9kYXNoYm9hcmQvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnXG5pbXBvcnQgeyBHcm91cFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9Hcm91cENvbnRleHQnXG5pbXBvcnQgR3JvdXBMaXN0IGZyb20gJ0AvY29tcG9uZW50cy9ncm91cHMvR3JvdXBMaXN0J1xuaW1wb3J0IEdyb3VwRGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9ncm91cHMvR3JvdXBEZXRhaWwnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XG4gIGNvbnN0IHsgdXNlciwgbG9hZGluZywgbG9nb3V0IH0gPSB1c2VBdXRoKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbG9hZGluZyAmJiAhdXNlcikge1xuICAgICAgcm91dGVyLnB1c2goJy9hdXRoJylcbiAgICB9XG4gIH0sIFt1c2VyLCBsb2FkaW5nLCByb3V0ZXJdKVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS01MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgaWYgKCF1c2VyKSB7XG4gICAgcmV0dXJuIG51bGwgLy8gV2lsbCByZWRpcmVjdCB0byBhdXRoXG4gIH1cblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgbG9nb3V0KClcbiAgICByb3V0ZXIucHVzaCgnL2F1dGgnKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8R3JvdXBQcm92aWRlcj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvd1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPk15QmluZGVyPC9oMT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgIFdlbGNvbWUsIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3VzZXIubmFtZSB8fCB1c2VyLnVzZXJuYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1yZWQtNTAwIGhvdmVyOmJnLXJlZC03MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktMiBweC00IHJvdW5kZWQgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgTG9nb3V0XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXhcIj5cbiAgICAgICAgICA8R3JvdXBMaXN0IC8+XG4gICAgICAgICAgPEdyb3VwRGV0YWlsIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9Hcm91cFByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlQXV0aCIsIkdyb3VwUHJvdmlkZXIiLCJHcm91cExpc3QiLCJHcm91cERldGFpbCIsIkRhc2hib2FyZFBhZ2UiLCJ1c2VyIiwibG9hZGluZyIsImxvZ291dCIsInJvdXRlciIsInB1c2giLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiaGFuZGxlTG9nb3V0IiwiaGVhZGVyIiwiaDEiLCJzcGFuIiwibmFtZSIsInVzZXJuYW1lIiwiYnV0dG9uIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/groups/GroupDetail.tsx":
/*!***********************************************!*\
  !*** ./src/components/groups/GroupDetail.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GroupDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/GroupContext */ \"(app-pages-browser)/./src/contexts/GroupContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_MessageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/MessageContext */ \"(app-pages-browser)/./src/contexts/MessageContext.tsx\");\n/* harmony import */ var _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NotesContext */ \"(app-pages-browser)/./src/contexts/NotesContext.tsx\");\n/* harmony import */ var _components_messages_ChatInterface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/messages/ChatInterface */ \"(app-pages-browser)/./src/components/messages/ChatInterface.tsx\");\n/* harmony import */ var _components_notes_NotesInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/notes/NotesInterface */ \"(app-pages-browser)/./src/components/notes/NotesInterface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction GroupDetail() {\n    _s();\n    const { selectedGroup, addMember } = (0,_contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [showAddMember, setShowAddMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memberEmail, setMemberEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [memberRole, setMemberRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('MEMBER');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('chat');\n    if (!selectedGroup) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1,\n                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Select a Group\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Choose a group from the sidebar to view details and start chatting\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    const isOwnerOrAdmin = selectedGroup.ownerId === (user === null || user === void 0 ? void 0 : user.id) || selectedGroup.members.some((m)=>m.userId === (user === null || user === void 0 ? void 0 : user.id) && [\n            'OWNER',\n            'ADMIN'\n        ].includes(m.role));\n    const handleAddMember = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        try {\n            await addMember(selectedGroup.id, memberEmail, memberRole);\n            setMemberEmail('');\n            setMemberRole('MEMBER');\n            setShowAddMember(false);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to add member');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_MessageContext__WEBPACK_IMPORTED_MODULE_4__.MessageProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotesContext__WEBPACK_IMPORTED_MODULE_5__.NotesProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-gray-200 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: selectedGroup.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    selectedGroup.isPrivate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-400 ml-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedGroup.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: selectedGroup.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    isOwnerOrAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddMember(true),\n                                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\",\n                                        children: \"Add Member\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('chat'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'chat' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: \"Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('members'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'members' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: [\n                                                \"Members (\",\n                                                selectedGroup._count.members,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('notes'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'notes' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: [\n                                                \"Notes (\",\n                                                selectedGroup._count.notes,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_messages_ChatInterface__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                group: selectedGroup\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'members' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-6 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-4 rounded-lg shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: selectedGroup._count.members\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Members\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-4 rounded-lg shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: selectedGroup._count.messages\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-4 rounded-lg shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: selectedGroup._count.notes\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Notes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border-b border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: \"Members\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: selectedGroup.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                                    children: (member.user.name || member.user.username).charAt(0).toUpperCase()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                    lineNumber: 158,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                lineNumber: 157,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: member.user.name || member.user.username\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                        lineNumber: 163,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: member.user.email\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                        lineNumber: 166,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                lineNumber: 162,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(member.role === 'OWNER' ? 'bg-green-100 text-green-800' : member.role === 'ADMIN' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                                                            children: member.role\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                            lineNumber: 170,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, member.id, true, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'notes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notes_NotesInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                group: selectedGroup\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    showAddMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Add Member\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAddMember(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleAddMember,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    value: memberEmail,\n                                                    onChange: (e)=>setMemberEmail(e.target.value),\n                                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                    placeholder: \"Enter user's email\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                                    children: \"Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: memberRole,\n                                                    onChange: (e)=>setMemberRole(e.target.value),\n                                                    className: \"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"MEMBER\",\n                                                            children: \"Member\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ADMIN\",\n                                                            children: \"Admin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowAddMember(false),\n                                                    className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\",\n                                                    children: loading ? 'Adding...' : 'Add Member'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"q/hHQIabC303H3COyA/WUk0PiCE=\", false, function() {\n    return [\n        _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/groups/GroupDetail.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/messages/ChatInterface.tsx":
/*!***************************************************!*\
  !*** ./src/components/messages/ChatInterface.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_MessageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/MessageContext */ \"(app-pages-browser)/./src/contexts/MessageContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatInterface(param) {\n    let { group } = param;\n    _s();\n    const { messages, loading, sending, sendMessage, loadMessages, hasMore, currentPage } = (0,_contexts_MessageContext__WEBPACK_IMPORTED_MODULE_2__.useMessages)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (group) {\n                loadMessages(group.id);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        group.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Scroll to bottom when new messages arrive\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || sending) return;\n        setError('');\n        const messageContent = newMessage.trim();\n        setNewMessage('');\n        try {\n            await sendMessage(group.id, messageContent);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to send message');\n            setNewMessage(messageContent); // Restore message on error\n        }\n    };\n    const loadMoreMessages = async ()=>{\n        if (hasMore && !loading) {\n            await loadMessages(group.id, currentPage + 1);\n        }\n    };\n    const formatTime = (date)=>{\n        return new Date(date).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const formatDate = (date)=>{\n        const messageDate = new Date(date);\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        if (messageDate.toDateString() === today.toDateString()) {\n            return 'Today';\n        } else if (messageDate.toDateString() === yesterday.toDateString()) {\n            return 'Yesterday';\n        } else {\n            return messageDate.toLocaleDateString();\n        }\n    };\n    const shouldShowDateSeparator = (currentMessage, previousMessage)=>{\n        if (!previousMessage) return true;\n        const currentDate = new Date(currentMessage.createdAt).toDateString();\n        const previousDate = new Date(previousMessage.createdAt).toDateString();\n        return currentDate !== previousDate;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesContainerRef,\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMoreMessages,\n                            disabled: loading,\n                            className: \"text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50\",\n                            children: loading ? 'Loading...' : 'Load older messages'\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    messages.map((message, index)=>{\n                        const previousMessage = index > 0 ? messages[index - 1] : null;\n                        const showDateSeparator = shouldShowDateSeparator(message, previousMessage);\n                        const isOwnMessage = message.authorId === (user === null || user === void 0 ? void 0 : user.id);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                showDateSeparator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full\",\n                                        children: formatDate(message.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(isOwnMessage ? 'justify-end' : 'justify-start'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-xs lg:max-w-md \".concat(isOwnMessage ? 'order-2' : 'order-1'),\n                                        children: [\n                                            !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-700 mb-1 font-medium\",\n                                                children: message.author.name || message.author.username\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 rounded-lg \".concat(isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900 border border-gray-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm whitespace-pre-wrap\",\n                                                        children: message.content.includes('```') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: message.content.split('```').map((part, index)=>index % 2 === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: part\n                                                                }, index, false, {\n                                                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto\",\n                                                                    children: part\n                                                                }, index, false, {\n                                                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, this) : message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1 \".concat(isOwnMessage ? 'text-blue-100' : 'text-gray-600'),\n                                                        children: formatTime(message.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    loading && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: \"Loading messages...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    !loading && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No messages yet. Start the conversation!\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSendMessage,\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: newMessage,\n                                onChange: (e)=>setNewMessage(e.target.value),\n                                placeholder: \"Type a message...\",\n                                className: \"flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500\",\n                                disabled: sending,\n                                maxLength: 2000\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !newMessage.trim() || sending,\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: sending ? 'Sending...' : 'Send'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"QWBTpf8mkHU/nfLur99xnWQUlAw=\", false, function() {\n    return [\n        _contexts_MessageContext__WEBPACK_IMPORTED_MODULE_2__.useMessages,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/messages/ChatInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const refreshUser = async ()=>{\n        try {\n            const response = await fetch('/api/auth/me');\n            if (response.ok) {\n                const data = await response.json();\n                setUser(data.user);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error('Failed to refresh user:', error);\n            setUser(null);\n        }\n    };\n    const login = async (email, password)=>{\n        const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Login failed');\n        }\n        const data = await response.json();\n        setUser(data.user);\n    };\n    const register = async (email, username, password, name)=>{\n        const response = await fetch('/api/auth/register', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                email,\n                username,\n                password,\n                name\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Registration failed');\n        }\n        // After successful registration, log the user in\n        await login(email, password);\n    };\n    const logout = async ()=>{\n        try {\n            await fetch('/api/auth/logout', {\n                method: 'POST'\n            });\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            refreshUser().finally({\n                \"AuthProvider.useEffect\": ()=>setLoading(false)\n            }[\"AuthProvider.useEffect\"]);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            login,\n            register,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/contexts/AuthContext.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/MessageContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/MessageContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageProvider: () => (/* binding */ MessageProvider),\n/* harmony export */   useMessages: () => (/* binding */ useMessages)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MessageProvider,useMessages auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst MessageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MessageProvider(param) {\n    let { children } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentGroupId, setCurrentGroupId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadMessages = async function(groupId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        if (groupId !== currentGroupId) {\n            // Reset state when switching groups\n            setMessages([]);\n            setCurrentPage(1);\n            setCurrentGroupId(groupId);\n            page = 1;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/groups/\".concat(groupId, \"/messages?page=\").concat(page, \"&limit=50\"));\n            if (response.ok) {\n                const data = await response.json();\n                if (page === 1) {\n                    setMessages(data.messages);\n                } else {\n                    // Append older messages for pagination\n                    setMessages((prev)=>[\n                            ...data.messages,\n                            ...prev\n                        ]);\n                }\n                setHasMore(data.pagination.hasMore);\n                setCurrentPage(page);\n            } else {\n                console.error('Failed to load messages');\n            }\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const sendMessage = async (groupId, content)=>{\n        setSending(true);\n        try {\n            const response = await fetch(\"/api/groups/\".concat(groupId, \"/messages\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to send message');\n            }\n            const result = await response.json();\n            // Add new message to the end of the list\n            setMessages((prev)=>[\n                    ...prev,\n                    result.data\n                ]);\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            throw error;\n        } finally{\n            setSending(false);\n        }\n    };\n    const clearMessages = ()=>{\n        setMessages([]);\n        setCurrentPage(1);\n        setHasMore(false);\n        setCurrentGroupId(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContext.Provider, {\n        value: {\n            messages,\n            loading,\n            sending,\n            sendMessage,\n            loadMessages,\n            clearMessages,\n            hasMore,\n            currentPage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/contexts/MessageContext.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageProvider, \"z3/WUbEYZliCzBEc+qHtQaNnvjc=\");\n_c = MessageProvider;\nfunction useMessages() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MessageContext);\n    if (context === undefined) {\n        throw new Error('useMessages must be used within a MessageProvider');\n    }\n    return context;\n}\n_s1(useMessages, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"MessageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/MessageContext.tsx\n"));

/***/ })

});