/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiL2hvbWUvcnlhbi9teWJpbmRlci9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fryan%2Fmybinder%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fryan%2Fmybinder%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRnJ5YW4lMkZteWJpbmRlciUyRnNyYyUyRmFwcCUyRmRhc2hib2FyZCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQW1GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9yeWFuL215YmluZGVyL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fryan%2Fmybinder%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9ob21lL3J5YW4vbXliaW5kZXIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/GroupContext */ \"(app-pages-browser)/./src/contexts/GroupContext.tsx\");\n/* harmony import */ var _components_groups_GroupList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/groups/GroupList */ \"(app-pages-browser)/./src/components/groups/GroupList.tsx\");\n/* harmony import */ var _components_groups_GroupDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/groups/GroupDetail */ \"(app-pages-browser)/./src/components/groups/GroupDetail.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user, loading, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null // Will redirect to auth\n        ;\n    }\n    const handleLogout = async ()=>{\n        await logout();\n        router.push('/auth');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_GroupContext__WEBPACK_IMPORTED_MODULE_4__.GroupProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white shadow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"MyBinder\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: [\n                                                \"Welcome, \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: user.name || user.username\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 28\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_groups_GroupList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_groups_GroupDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/app/dashboard/page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"JjlT7r8cdmwnjySuxpKGq9HDRxs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/groups/CreateGroupModal.tsx":
/*!****************************************************!*\
  !*** ./src/components/groups/CreateGroupModal.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateGroupModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/GroupContext */ \"(app-pages-browser)/./src/contexts/GroupContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CreateGroupModal(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPrivate, setIsPrivate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { createGroup } = (0,_contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        try {\n            await createGroup({\n                name,\n                description: description || undefined,\n                isPrivate\n            });\n            // Reset form and close modal\n            setName('');\n            setDescription('');\n            setIsPrivate(false);\n            onClose();\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to create group');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        if (!loading) {\n            setName('');\n            setDescription('');\n            setIsPrivate(false);\n            setError('');\n            onClose();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Create New Group\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            disabled: loading,\n                            className: \"text-gray-400 hover:text-gray-600 disabled:opacity-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"name\",\n                                    children: \"Group Name *\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"name\",\n                                    type: \"text\",\n                                    value: name,\n                                    onChange: (e)=>setName(e.target.value),\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"Enter group name\",\n                                    required: true,\n                                    maxLength: 100\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"description\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"Enter group description (optional)\",\n                                    rows: 3,\n                                    maxLength: 500\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: isPrivate,\n                                            onChange: (e)=>setIsPrivate(e.target.checked),\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-700 text-sm\",\n                                            children: \"Private Group\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Private groups require invitation to join\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    disabled: loading,\n                                    className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading || !name.trim(),\n                                    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? 'Creating...' : 'Create Group'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateGroupModal, \"HiFxIhouGIBduCbo5lBjTIR2IOI=\", false, function() {\n    return [\n        _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups\n    ];\n});\n_c = CreateGroupModal;\nvar _c;\n$RefreshReg$(_c, \"CreateGroupModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/groups/CreateGroupModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/groups/GroupDetail.tsx":
/*!***********************************************!*\
  !*** ./src/components/groups/GroupDetail.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GroupDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/GroupContext */ \"(app-pages-browser)/./src/contexts/GroupContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_MessageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/MessageContext */ \"(app-pages-browser)/./src/contexts/MessageContext.tsx\");\n/* harmony import */ var _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NotesContext */ \"(app-pages-browser)/./src/contexts/NotesContext.tsx\");\n/* harmony import */ var _components_messages_ChatInterface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/messages/ChatInterface */ \"(app-pages-browser)/./src/components/messages/ChatInterface.tsx\");\n/* harmony import */ var _components_notes_NotesInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/notes/NotesInterface */ \"(app-pages-browser)/./src/components/notes/NotesInterface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction GroupDetail() {\n    _s();\n    const { selectedGroup, addMember } = (0,_contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [showAddMember, setShowAddMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memberEmail, setMemberEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [memberRole, setMemberRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('MEMBER');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('chat');\n    if (!selectedGroup) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1,\n                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Select a Group\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Choose a group from the sidebar to view details and start chatting\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    const isOwnerOrAdmin = selectedGroup.ownerId === (user === null || user === void 0 ? void 0 : user.id) || selectedGroup.members.some((m)=>m.userId === (user === null || user === void 0 ? void 0 : user.id) && [\n            'OWNER',\n            'ADMIN'\n        ].includes(m.role));\n    const handleAddMember = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        try {\n            await addMember(selectedGroup.id, memberEmail, memberRole);\n            setMemberEmail('');\n            setMemberRole('MEMBER');\n            setShowAddMember(false);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to add member');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_MessageContext__WEBPACK_IMPORTED_MODULE_4__.MessageProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotesContext__WEBPACK_IMPORTED_MODULE_5__.NotesProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-gray-200 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: selectedGroup.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    selectedGroup.isPrivate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-400 ml-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedGroup.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: selectedGroup.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    isOwnerOrAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddMember(true),\n                                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\",\n                                        children: \"Add Member\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('chat'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'chat' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: \"Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('members'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'members' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: [\n                                                \"Members (\",\n                                                selectedGroup._count.members,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('notes'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'notes' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                            children: [\n                                                \"Notes (\",\n                                                selectedGroup._count.notes,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_messages_ChatInterface__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                group: selectedGroup\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'members' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-6 overflow-y-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-4 rounded-lg shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: selectedGroup._count.members\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Members\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-4 rounded-lg shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: selectedGroup._count.messages\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Messages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-4 rounded-lg shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: selectedGroup._count.notes\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Notes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border-b border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: \"Members\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: selectedGroup.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                                    children: (member.user.name || member.user.username).charAt(0).toUpperCase()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                    lineNumber: 158,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                lineNumber: 157,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                                        children: member.user.name || member.user.username\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                        lineNumber: 163,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: member.user.email\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                        lineNumber: 166,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                                lineNumber: 162,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(member.role === 'OWNER' ? 'bg-green-100 text-green-800' : member.role === 'ADMIN' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'),\n                                                                            children: member.role\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                            lineNumber: 170,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, member.id, true, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'notes' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notes_NotesInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                group: selectedGroup\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    showAddMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Add Member\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAddMember(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleAddMember,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    value: memberEmail,\n                                                    onChange: (e)=>setMemberEmail(e.target.value),\n                                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                    placeholder: \"Enter user's email\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                                    children: \"Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: memberRole,\n                                                    onChange: (e)=>setMemberRole(e.target.value),\n                                                    className: \"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"MEMBER\",\n                                                            children: \"Member\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"ADMIN\",\n                                                            children: \"Admin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowAddMember(false),\n                                                    className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\",\n                                                    children: loading ? 'Adding...' : 'Add Member'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupDetail.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"q/hHQIabC303H3COyA/WUk0PiCE=\", false, function() {\n    return [\n        _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/groups/GroupDetail.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/groups/GroupList.tsx":
/*!*********************************************!*\
  !*** ./src/components/groups/GroupList.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GroupList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/GroupContext */ \"(app-pages-browser)/./src/contexts/GroupContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _CreateGroupModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateGroupModal */ \"(app-pages-browser)/./src/components/groups/CreateGroupModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction GroupList() {\n    _s();\n    const { groups, selectedGroup, selectGroup, loading } = (0,_contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-80 bg-white border-r border-gray-200 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-3/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(3)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-white border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Groups\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreateModal(true),\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full\",\n                                    title: \"Create new group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: groups.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-2\",\n                                    children: \"No groups yet\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreateModal(true),\n                                    className: \"text-blue-500 hover:text-blue-600 text-sm\",\n                                    children: \"Create your first group\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2\",\n                            children: groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>selectGroup(group),\n                                    className: \"p-3 rounded-lg cursor-pointer mb-2 transition-colors \".concat((selectedGroup === null || selectedGroup === void 0 ? void 0 : selectedGroup.id) === group.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                children: group.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 74,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            group.isPrivate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 text-gray-400 ml-1\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                    lineNumber: 79,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 78,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    group.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate mt-1\",\n                                                        children: group.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-2 text-xs text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    group._count.members,\n                                                                    \" members\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 89,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mx-1\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    group._count.messages,\n                                                                    \" messages\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mx-1\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    group._count.notes,\n                                                                    \" notes\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 21\n                                            }, this),\n                                            group.ownerId === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                    children: \"Owner\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, this)\n                                }, group.id, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateGroupModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showCreateModal,\n                onClose: ()=>setShowCreateModal(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/groups/GroupList.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GroupList, \"6yogRHf0iTnwv2YgyO1Ym9YaQtU=\", false, function() {\n    return [\n        _contexts_GroupContext__WEBPACK_IMPORTED_MODULE_2__.useGroups,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = GroupList;\nvar _c;\n$RefreshReg$(_c, \"GroupList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/groups/GroupList.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/messages/ChatInterface.tsx":
/*!***************************************************!*\
  !*** ./src/components/messages/ChatInterface.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_MessageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/MessageContext */ \"(app-pages-browser)/./src/contexts/MessageContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatInterface(param) {\n    let { group } = param;\n    _s();\n    const { messages, loading, sending, sendMessage, loadMessages, hasMore, currentPage } = (0,_contexts_MessageContext__WEBPACK_IMPORTED_MODULE_2__.useMessages)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (group) {\n                loadMessages(group.id);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        group.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Scroll to bottom when new messages arrive\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || sending) return;\n        setError('');\n        const messageContent = newMessage.trim();\n        setNewMessage('');\n        try {\n            await sendMessage(group.id, messageContent);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to send message');\n            setNewMessage(messageContent); // Restore message on error\n        }\n    };\n    const loadMoreMessages = async ()=>{\n        if (hasMore && !loading) {\n            await loadMessages(group.id, currentPage + 1);\n        }\n    };\n    const formatTime = (date)=>{\n        return new Date(date).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const formatDate = (date)=>{\n        const messageDate = new Date(date);\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        if (messageDate.toDateString() === today.toDateString()) {\n            return 'Today';\n        } else if (messageDate.toDateString() === yesterday.toDateString()) {\n            return 'Yesterday';\n        } else {\n            return messageDate.toLocaleDateString();\n        }\n    };\n    const shouldShowDateSeparator = (currentMessage, previousMessage)=>{\n        if (!previousMessage) return true;\n        const currentDate = new Date(currentMessage.createdAt).toDateString();\n        const previousDate = new Date(previousMessage.createdAt).toDateString();\n        return currentDate !== previousDate;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesContainerRef,\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMoreMessages,\n                            disabled: loading,\n                            className: \"text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50\",\n                            children: loading ? 'Loading...' : 'Load older messages'\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    messages.map((message, index)=>{\n                        const previousMessage = index > 0 ? messages[index - 1] : null;\n                        const showDateSeparator = shouldShowDateSeparator(message, previousMessage);\n                        const isOwnMessage = message.authorId === (user === null || user === void 0 ? void 0 : user.id);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                showDateSeparator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full\",\n                                        children: formatDate(message.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(isOwnMessage ? 'justify-end' : 'justify-start'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-xs lg:max-w-md \".concat(isOwnMessage ? 'order-2' : 'order-1'),\n                                        children: [\n                                            !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-700 mb-1 font-medium\",\n                                                children: message.author.name || message.author.username\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 rounded-lg \".concat(isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900 border border-gray-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm whitespace-pre-wrap\",\n                                                        children: message.content.includes('```') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: message.content.split('```').map((part, index)=>index % 2 === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: part\n                                                                }, index, false, {\n                                                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto\",\n                                                                    children: part\n                                                                }, index, false, {\n                                                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, this) : message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1 \".concat(isOwnMessage ? 'text-blue-100' : 'text-gray-600'),\n                                                        children: formatTime(message.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    loading && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: \"Loading messages...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    !loading && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No messages yet. Start the conversation!\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSendMessage,\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: newMessage,\n                                onChange: (e)=>setNewMessage(e.target.value),\n                                placeholder: \"Type a message...\",\n                                className: \"flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500\",\n                                disabled: sending,\n                                maxLength: 2000\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !newMessage.trim() || sending,\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: sending ? 'Sending...' : 'Send'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/mybinder/src/components/messages/ChatInterface.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"QWBTpf8mkHU/nfLur99xnWQUlAw=\", false, function() {\n    return [\n        _contexts_MessageContext__WEBPACK_IMPORTED_MODULE_2__.useMessages,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/messages/ChatInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/notes/BlockEditor.tsx":
/*!**********************************************!*\
  !*** ./src/components/notes/BlockEditor.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlockEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/NotesContext */ \"(app-pages-browser)/./src/contexts/NotesContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction BlockEditor(param) {\n    let { block, noteId, isLast, onAddBlock } = param;\n    _s();\n    const { updateBlock, deleteBlock } = (0,_contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes)();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(block.content);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlockEditor.useEffect\": ()=>{\n            setContent(block.content);\n        }\n    }[\"BlockEditor.useEffect\"], [\n        block.content\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlockEditor.useEffect\": ()=>{\n            if (isEditing && textareaRef.current) {\n                textareaRef.current.focus();\n                // Auto-resize textarea\n                textareaRef.current.style.height = 'auto';\n                textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';\n            }\n        }\n    }[\"BlockEditor.useEffect\"], [\n        isEditing\n    ]);\n    const handleSave = async ()=>{\n        try {\n            await updateBlock(block.id, {\n                content\n            });\n            setIsEditing(false);\n        } catch (error) {\n            console.error('Failed to update block:', error);\n            alert('Failed to update block');\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            if (isLast && content.trim()) {\n                onAddBlock('TEXT');\n            } else {\n                handleSave();\n            }\n        } else if (e.key === 'Escape') {\n            setContent(block.content);\n            setIsEditing(false);\n        }\n    };\n    const handleDelete = async ()=>{\n        if (confirm('Are you sure you want to delete this block?')) {\n            try {\n                await deleteBlock(block.id);\n            } catch (error) {\n                console.error('Failed to delete block:', error);\n                alert('Failed to delete block');\n            }\n        }\n    };\n    const handleTypeChange = async (newType)=>{\n        try {\n            await updateBlock(block.id, {\n                type: newType\n            });\n            setShowMenu(false);\n        } catch (error) {\n            console.error('Failed to update block type:', error);\n            alert('Failed to update block type');\n        }\n    };\n    const getBlockIcon = (type)=>{\n        switch(type){\n            case 'HEADING':\n                return '📰';\n            case 'BULLET_LIST':\n                return '•';\n            case 'NUMBERED_LIST':\n                return '1.';\n            case 'CODE':\n                return '💻';\n            case 'QUOTE':\n                return '💬';\n            default:\n                return '📝';\n        }\n    };\n    const getBlockStyle = (type)=>{\n        switch(type){\n            case 'HEADING':\n                return 'text-xl font-bold text-gray-900';\n            case 'CODE':\n                return 'code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto';\n            case 'QUOTE':\n                return 'border-l-4 border-blue-400 pl-4 italic text-gray-700 bg-blue-50 py-2 rounded-r';\n            default:\n                return 'text-gray-900';\n        }\n    };\n    const renderContent = ()=>{\n        if (isEditing) {\n            const editingStyle = block.type === 'CODE' ? 'w-full border-none outline-none resize-none code-block code-dark text-green-400 p-4 rounded-lg text-sm overflow-x-auto' : \"w-full border-none outline-none resize-none \".concat(getBlockStyle(block.type), \" bg-transparent\");\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                ref: textareaRef,\n                value: content,\n                onChange: (e)=>setContent(e.target.value),\n                onKeyDown: handleKeyDown,\n                onBlur: handleSave,\n                className: editingStyle,\n                placeholder: block.type === 'CODE' ? 'Enter your code here...' : 'Type something...',\n                rows: block.type === 'CODE' ? 3 : 1\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this);\n        }\n        if (!content.trim()) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: ()=>setIsEditing(true),\n                className: \"text-gray-400 cursor-text py-2\",\n                children: \"Type something...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        const formattedContent = block.type === 'BULLET_LIST' ? content.split('\\n').map((line)=>line.trim() ? \"• \".concat(line) : line).join('\\n') : block.type === 'NUMBERED_LIST' ? content.split('\\n').map((line, i)=>line.trim() ? \"\".concat(i + 1, \". \").concat(line) : line).join('\\n') : content;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>setIsEditing(true),\n            className: \"cursor-text py-2 whitespace-pre-wrap \".concat(getBlockStyle(block.type)),\n            children: formattedContent\n        }, void 0, false, {\n            fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative flex items-start space-x-2 py-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowMenu(!showMenu),\n                        className: \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity\",\n                        title: \"Change block type\",\n                        children: getBlockIcon(block.type)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTypeChange('TEXT'),\n                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                    children: \"\\uD83D\\uDCDD Text\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTypeChange('HEADING'),\n                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                    children: \"\\uD83D\\uDCF0 Heading\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTypeChange('BULLET_LIST'),\n                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                    children: \"• Bullet List\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTypeChange('NUMBERED_LIST'),\n                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                    children: \"1. Numbered List\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTypeChange('CODE'),\n                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                    children: \"\\uD83D\\uDCBB Code\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTypeChange('QUOTE'),\n                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                    children: \"\\uD83D\\uDCAC Quote\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"opacity-0 group-hover:opacity-100 transition-opacity\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500\",\n                    title: \"Delete block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/mybinder/src/components/notes/BlockEditor.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(BlockEditor, \"3FI78Gg+n9QDs15lrwBqjtvyL1M=\", false, function() {\n    return [\n        _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes\n    ];\n});\n_c = BlockEditor;\nvar _c;\n$RefreshReg$(_c, \"BlockEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/notes/BlockEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/notes/CreateNoteModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/notes/CreateNoteModal.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/NotesContext */ \"(app-pages-browser)/./src/contexts/NotesContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CreateNoteModal(param) {\n    let { isOpen, onClose, groupId } = param;\n    _s();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { createNote } = (0,_contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setLoading(true);\n        try {\n            await createNote(groupId, {\n                title,\n                description: description || undefined,\n                groupId\n            });\n            // Reset form and close modal\n            setTitle('');\n            setDescription('');\n            onClose();\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to create note');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        if (!loading) {\n            setTitle('');\n            setDescription('');\n            setError('');\n            onClose();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Create New Note\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            disabled: loading,\n                            className: \"text-gray-400 hover:text-gray-600 disabled:opacity-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"title\",\n                                    children: \"Note Title *\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"title\",\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"Enter note title\",\n                                    required: true,\n                                    maxLength: 200\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"description\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"Enter note description (optional)\",\n                                    rows: 3,\n                                    maxLength: 500\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    disabled: loading,\n                                    className: \"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading || !title.trim(),\n                                    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? 'Creating...' : 'Create Note'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/components/notes/CreateNoteModal.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateNoteModal, \"9lXmu6pwXjRCC9VGobMm/PVyemQ=\", false, function() {\n    return [\n        _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes\n    ];\n});\n_c = CreateNoteModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CreateNoteModal);\nvar _c;\n$RefreshReg$(_c, \"CreateNoteModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/notes/CreateNoteModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/notes/NoteEditor.tsx":
/*!*********************************************!*\
  !*** ./src/components/notes/NoteEditor.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/NotesContext */ \"(app-pages-browser)/./src/contexts/NotesContext.tsx\");\n/* harmony import */ var _BlockEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BlockEditor */ \"(app-pages-browser)/./src/components/notes/BlockEditor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NoteEditor(param) {\n    let { note: initialNote } = param;\n    _s();\n    const { selectedNote, updateNote, createBlock, loadNote, loadingNote } = (0,_contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes)();\n    const [isEditingTitle, setIsEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialNote.title);\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialNote.description || '');\n    const note = selectedNote || initialNote;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoteEditor.useEffect\": ()=>{\n            if (initialNote.id) {\n                loadNote(initialNote.id);\n            }\n        }\n    }[\"NoteEditor.useEffect\"], [\n        initialNote.id\n    ]);\n    const handleSaveTitle = async ()=>{\n        try {\n            await updateNote(note.id, {\n                title,\n                description: description || undefined\n            });\n            setIsEditingTitle(false);\n        } catch (error) {\n            console.error('Failed to update note:', error);\n            alert('Failed to update note');\n        }\n    };\n    const handleAddBlock = async function() {\n        let type = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'TEXT';\n        try {\n            const newOrder = note.blocks.length;\n            await createBlock(note.id, type, '', newOrder);\n        } catch (error) {\n            console.error('Failed to create block:', error);\n            alert('Failed to create block');\n        }\n    };\n    const formatDate = (date)=>{\n        return new Date(date).toLocaleDateString([], {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 p-6\",\n                children: [\n                    isEditingTitle ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: title,\n                                onChange: (e)=>setTitle(e.target.value),\n                                className: \"text-2xl font-bold text-gray-900 w-full border-none outline-none bg-transparent\",\n                                placeholder: \"Note title\",\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: description,\n                                onChange: (e)=>setDescription(e.target.value),\n                                className: \"text-gray-600 w-full border-none outline-none bg-transparent resize-none\",\n                                placeholder: \"Add a description...\",\n                                rows: 2\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSaveTitle,\n                                        className: \"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setTitle(note.title);\n                                            setDescription(note.description || '');\n                                            setIsEditingTitle(false);\n                                        },\n                                        className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>setIsEditingTitle(true),\n                        className: \"cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2 hover:bg-gray-50 p-2 rounded\",\n                                children: note.title\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            note.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 hover:bg-gray-50 p-2 rounded\",\n                                children: note.description\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Created by \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: note.author.name || note.author.username\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Last updated \",\n                                    formatDate(note.updatedAt)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto p-6\",\n                    children: [\n                        (loadingNote || !note.blocks) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-gray-500\",\n                                    children: \"Loading note content...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        note.blocks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: note.blocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlockEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    block: block,\n                                    noteId: note.id,\n                                    isLast: index === note.blocks.length - 1,\n                                    onAddBlock: handleAddBlock\n                                }, block.id, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        note.blocks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAddBlock('TEXT'),\n                                        className: \"flex items-center space-x-2 text-gray-500 hover:text-gray-700 p-2 rounded hover:bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Add block\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAddBlock('TEXT'),\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"\\uD83D\\uDCDD Text\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAddBlock('HEADING'),\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"\\uD83D\\uDCF0 Heading\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAddBlock('BULLET_LIST'),\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"• Bullet List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAddBlock('NUMBERED_LIST'),\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"1. Numbered List\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAddBlock('CODE'),\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"\\uD83D\\uDCBB Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleAddBlock('QUOTE'),\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                            children: \"\\uD83D\\uDCAC Quote\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/mybinder/src/components/notes/NoteEditor.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(NoteEditor, \"N6JTOd0+efAiUZbsHMh5AFO3E8M=\", false, function() {\n    return [\n        _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes\n    ];\n});\n_c = NoteEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoteEditor);\nvar _c;\n$RefreshReg$(_c, \"NoteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL25vdGVzL05vdGVFZGl0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ087QUFFWDtBQU12QyxTQUFTSSxXQUFXLEtBQXNDO1FBQXRDLEVBQUVDLE1BQU1DLFdBQVcsRUFBbUIsR0FBdEM7O0lBQ2xCLE1BQU0sRUFBRUMsWUFBWSxFQUFFQyxVQUFVLEVBQUVDLFdBQVcsRUFBRUMsUUFBUSxFQUFFQyxXQUFXLEVBQUUsR0FBR1QsZ0VBQVFBO0lBQ2pGLE1BQU0sQ0FBQ1UsZ0JBQWdCQyxrQkFBa0IsR0FBR2IsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDYyxPQUFPQyxTQUFTLEdBQUdmLCtDQUFRQSxDQUFDTSxZQUFZUSxLQUFLO0lBQ3BELE1BQU0sQ0FBQ0UsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUNNLFlBQVlVLFdBQVcsSUFBSTtJQUUxRSxNQUFNWCxPQUFPRSxnQkFBZ0JEO0lBRTdCTCxnREFBU0E7Z0NBQUM7WUFDUixJQUFJSyxZQUFZWSxFQUFFLEVBQUU7Z0JBQ2xCUixTQUFTSixZQUFZWSxFQUFFO1lBQ3pCO1FBQ0Y7K0JBQUc7UUFBQ1osWUFBWVksRUFBRTtLQUFDO0lBRW5CLE1BQU1DLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTVgsV0FBV0gsS0FBS2EsRUFBRSxFQUFFO2dCQUFFSjtnQkFBT0UsYUFBYUEsZUFBZUk7WUFBVTtZQUN6RVAsa0JBQWtCO1FBQ3BCLEVBQUUsT0FBT1EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q0UsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUI7WUFBT0Msd0VBQWU7UUFDM0MsSUFBSTtZQUNGLE1BQU1DLFdBQVdyQixLQUFLc0IsTUFBTSxDQUFDQyxNQUFNO1lBQ25DLE1BQU1uQixZQUFZSixLQUFLYSxFQUFFLEVBQUVPLE1BQU0sSUFBSUM7UUFDdkMsRUFBRSxPQUFPTCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDRSxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1NLGFBQWEsQ0FBQ0M7UUFDbEIsT0FBTyxJQUFJQyxLQUFLRCxNQUFNRSxrQkFBa0IsQ0FBQyxFQUFFLEVBQUU7WUFDM0NDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxLQUFLO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztvQkFDWjNCLCtCQUNDLDhEQUFDMEI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FDQ2YsTUFBSztnQ0FDTGdCLE9BQU8zQjtnQ0FDUDRCLFVBQVUsQ0FBQ0MsSUFBTTVCLFNBQVM0QixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0NBQ3hDRixXQUFVO2dDQUNWTSxhQUFZO2dDQUNaQyxTQUFTOzs7Ozs7MENBRVgsOERBQUNDO2dDQUNDTixPQUFPekI7Z0NBQ1AwQixVQUFVLENBQUNDLElBQU0xQixlQUFlMEIsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dDQUM5Q0YsV0FBVTtnQ0FDVk0sYUFBWTtnQ0FDWkcsTUFBTTs7Ozs7OzBDQUVSLDhEQUFDVjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNVO3dDQUNDQyxTQUFTL0I7d0NBQ1RvQixXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUNVO3dDQUNDQyxTQUFTOzRDQUNQbkMsU0FBU1YsS0FBS1MsS0FBSzs0Q0FDbkJHLGVBQWVaLEtBQUtXLFdBQVcsSUFBSTs0Q0FDbkNILGtCQUFrQjt3Q0FDcEI7d0NBQ0EwQixXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs2Q0FNTCw4REFBQ0Q7d0JBQUlZLFNBQVMsSUFBTXJDLGtCQUFrQjt3QkFBTzBCLFdBQVU7OzBDQUNyRCw4REFBQ1k7Z0NBQUdaLFdBQVU7MENBQ1hsQyxLQUFLUyxLQUFLOzs7Ozs7NEJBRVpULEtBQUtXLFdBQVcsa0JBQ2YsOERBQUNvQztnQ0FBRWIsV0FBVTswQ0FDVmxDLEtBQUtXLFdBQVc7Ozs7Ozs7Ozs7OztrQ0FNekIsOERBQUNzQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztvQ0FBSTtrREFDUSw4REFBQ2U7d0NBQUtkLFdBQVU7a0RBQWVsQyxLQUFLaUQsTUFBTSxDQUFDQyxJQUFJLElBQUlsRCxLQUFLaUQsTUFBTSxDQUFDRSxRQUFROzs7Ozs7Ozs7Ozs7MENBRXBGLDhEQUFDbEI7O29DQUFJO29DQUNXVCxXQUFXeEIsS0FBS29ELFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTdDLDhEQUFDbkI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzt3QkFFWDVCLENBQUFBLGVBQWUsQ0FBQ04sS0FBS3NCLE1BQU0sbUJBQzNCLDhEQUFDVzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNjO29DQUFLZCxXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7O3dCQUt4Q2xDLEtBQUtzQixNQUFNLGtCQUNWLDhEQUFDVzs0QkFBSUMsV0FBVTtzQ0FDWmxDLEtBQUtzQixNQUFNLENBQUMrQixHQUFHLENBQUMsQ0FBQ0MsT0FBT0Msc0JBQ3ZCLDhEQUFDekQsb0RBQVdBO29DQUVWd0QsT0FBT0E7b0NBQ1BFLFFBQVF4RCxLQUFLYSxFQUFFO29DQUNmNEMsUUFBUUYsVUFBVXZELEtBQUtzQixNQUFNLENBQUNDLE1BQU0sR0FBRztvQ0FDdkNtQyxZQUFZdkM7bUNBSlBtQyxNQUFNekMsRUFBRTs7Ozs7Ozs7Ozt3QkFXcEJiLEtBQUtzQixNQUFNLGtCQUNWLDhEQUFDVzs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDVTt3Q0FDQ0MsU0FBUyxJQUFNMUIsZUFBZTt3Q0FDOUJlLFdBQVU7OzBEQUVWLDhEQUFDeUI7Z0RBQUl6QixXQUFVO2dEQUFVMEIsTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDakUsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7MERBRXZFLDhEQUFDbkI7Z0RBQUtkLFdBQVU7MERBQVU7Ozs7Ozs7Ozs7OztrREFJOUIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1U7Z0RBQU9WLFdBQVU7MERBQ2hCLDRFQUFDeUI7b0RBQUl6QixXQUFVO29EQUFVMEIsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDakUsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFHekUsOERBQUNsQztnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDVTs0REFDQ0MsU0FBUyxJQUFNMUIsZUFBZTs0REFDOUJlLFdBQVU7c0VBQ1g7Ozs7OztzRUFHRCw4REFBQ1U7NERBQ0NDLFNBQVMsSUFBTTFCLGVBQWU7NERBQzlCZSxXQUFVO3NFQUNYOzs7Ozs7c0VBR0QsOERBQUNVOzREQUNDQyxTQUFTLElBQU0xQixlQUFlOzREQUM5QmUsV0FBVTtzRUFDWDs7Ozs7O3NFQUdELDhEQUFDVTs0REFDQ0MsU0FBUyxJQUFNMUIsZUFBZTs0REFDOUJlLFdBQVU7c0VBQ1g7Ozs7OztzRUFHRCw4REFBQ1U7NERBQ0NDLFNBQVMsSUFBTTFCLGVBQWU7NERBQzlCZSxXQUFVO3NFQUNYOzs7Ozs7c0VBR0QsOERBQUNVOzREQUNDQyxTQUFTLElBQU0xQixlQUFlOzREQUM5QmUsV0FBVTtzRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBYXJCO0dBMU1TbkM7O1FBQ2tFRiw0REFBUUE7OztLQUQxRUU7QUE0TVQsaUVBQWVBLFVBQVVBLEVBQUEiLCJzb3VyY2VzIjpbIi9ob21lL3J5YW4vbXliaW5kZXIvc3JjL2NvbXBvbmVudHMvbm90ZXMvTm90ZUVkaXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZU5vdGVzIH0gZnJvbSAnQC9jb250ZXh0cy9Ob3Rlc0NvbnRleHQnXG5pbXBvcnQgeyBOb3RlV2l0aEJsb2NrcyB9IGZyb20gJ0AvdHlwZXMnXG5pbXBvcnQgQmxvY2tFZGl0b3IgZnJvbSAnLi9CbG9ja0VkaXRvcidcblxuaW50ZXJmYWNlIE5vdGVFZGl0b3JQcm9wcyB7XG4gIG5vdGU6IE5vdGVXaXRoQmxvY2tzXG59XG5cbmZ1bmN0aW9uIE5vdGVFZGl0b3IoeyBub3RlOiBpbml0aWFsTm90ZSB9OiBOb3RlRWRpdG9yUHJvcHMpIHtcbiAgY29uc3QgeyBzZWxlY3RlZE5vdGUsIHVwZGF0ZU5vdGUsIGNyZWF0ZUJsb2NrLCBsb2FkTm90ZSwgbG9hZGluZ05vdGUgfSA9IHVzZU5vdGVzKClcbiAgY29uc3QgW2lzRWRpdGluZ1RpdGxlLCBzZXRJc0VkaXRpbmdUaXRsZV0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3RpdGxlLCBzZXRUaXRsZV0gPSB1c2VTdGF0ZShpbml0aWFsTm90ZS50aXRsZSlcbiAgY29uc3QgW2Rlc2NyaXB0aW9uLCBzZXREZXNjcmlwdGlvbl0gPSB1c2VTdGF0ZShpbml0aWFsTm90ZS5kZXNjcmlwdGlvbiB8fCAnJylcblxuICBjb25zdCBub3RlID0gc2VsZWN0ZWROb3RlIHx8IGluaXRpYWxOb3RlXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaW5pdGlhbE5vdGUuaWQpIHtcbiAgICAgIGxvYWROb3RlKGluaXRpYWxOb3RlLmlkKVxuICAgIH1cbiAgfSwgW2luaXRpYWxOb3RlLmlkXSlcblxuICBjb25zdCBoYW5kbGVTYXZlVGl0bGUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHVwZGF0ZU5vdGUobm90ZS5pZCwgeyB0aXRsZSwgZGVzY3JpcHRpb246IGRlc2NyaXB0aW9uIHx8IHVuZGVmaW5lZCB9KVxuICAgICAgc2V0SXNFZGl0aW5nVGl0bGUoZmFsc2UpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgbm90ZTonLCBlcnJvcilcbiAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gdXBkYXRlIG5vdGUnKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUFkZEJsb2NrID0gYXN5bmMgKHR5cGU6IHN0cmluZyA9ICdURVhUJykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBuZXdPcmRlciA9IG5vdGUuYmxvY2tzLmxlbmd0aFxuICAgICAgYXdhaXQgY3JlYXRlQmxvY2sobm90ZS5pZCwgdHlwZSwgJycsIG5ld09yZGVyKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIGJsb2NrOicsIGVycm9yKVxuICAgICAgYWxlcnQoJ0ZhaWxlZCB0byBjcmVhdGUgYmxvY2snKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZTogc3RyaW5nIHwgRGF0ZSkgPT4ge1xuICAgIHJldHVybiBuZXcgRGF0ZShkYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoW10sIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0J1xuICAgIH0pXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIHsvKiBOb3RlIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICB7aXNFZGl0aW5nVGl0bGUgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHZhbHVlPXt0aXRsZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRUaXRsZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIHctZnVsbCBib3JkZXItbm9uZSBvdXRsaW5lLW5vbmUgYmctdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk5vdGUgdGl0bGVcIlxuICAgICAgICAgICAgICBhdXRvRm9jdXNcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgdmFsdWU9e2Rlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldERlc2NyaXB0aW9uKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB3LWZ1bGwgYm9yZGVyLW5vbmUgb3V0bGluZS1ub25lIGJnLXRyYW5zcGFyZW50IHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBZGQgYSBkZXNjcmlwdGlvbi4uLlwiXG4gICAgICAgICAgICAgIHJvd3M9ezJ9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZVRpdGxlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtMyBweS0xIHJvdW5kZWQgdGV4dC1zbVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBTYXZlXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2V0VGl0bGUobm90ZS50aXRsZSlcbiAgICAgICAgICAgICAgICAgIHNldERlc2NyaXB0aW9uKG5vdGUuZGVzY3JpcHRpb24gfHwgJycpXG4gICAgICAgICAgICAgICAgICBzZXRJc0VkaXRpbmdUaXRsZShmYWxzZSlcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNDAwIHRleHQtZ3JheS03MDAgcHgtMyBweS0xIHJvdW5kZWQgdGV4dC1zbVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IG9uQ2xpY2s9eygpID0+IHNldElzRWRpdGluZ1RpdGxlKHRydWUpfSBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTIgaG92ZXI6YmctZ3JheS01MCBwLTIgcm91bmRlZFwiPlxuICAgICAgICAgICAgICB7bm90ZS50aXRsZX1cbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICB7bm90ZS5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS01MCBwLTIgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgIHtub3RlLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbXQtNCB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgQ3JlYXRlZCBieSA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntub3RlLmF1dGhvci5uYW1lIHx8IG5vdGUuYXV0aG9yLnVzZXJuYW1lfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgTGFzdCB1cGRhdGVkIHtmb3JtYXREYXRlKG5vdGUudXBkYXRlZEF0KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE5vdGUgQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHAtNlwiPlxuICAgICAgICAgIHsvKiBMb2FkaW5nIFN0YXRlICovfVxuICAgICAgICAgIHsobG9hZGluZ05vdGUgfHwgIW5vdGUuYmxvY2tzKSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS01MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LWdyYXktNTAwXCI+TG9hZGluZyBub3RlIGNvbnRlbnQuLi48L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIEJsb2NrcyAqL31cbiAgICAgICAgICB7bm90ZS5ibG9ja3MgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge25vdGUuYmxvY2tzLm1hcCgoYmxvY2ssIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPEJsb2NrRWRpdG9yXG4gICAgICAgICAgICAgICAgICBrZXk9e2Jsb2NrLmlkfVxuICAgICAgICAgICAgICAgICAgYmxvY2s9e2Jsb2NrfVxuICAgICAgICAgICAgICAgICAgbm90ZUlkPXtub3RlLmlkfVxuICAgICAgICAgICAgICAgICAgaXNMYXN0PXtpbmRleCA9PT0gbm90ZS5ibG9ja3MubGVuZ3RoIC0gMX1cbiAgICAgICAgICAgICAgICAgIG9uQWRkQmxvY2s9e2hhbmRsZUFkZEJsb2NrfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBBZGQgQmxvY2sgQnV0dG9uICovfVxuICAgICAgICAgIHtub3RlLmJsb2NrcyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBZGRCbG9jaygnVEVYVCcpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBwLTIgcm91bmRlZCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA0djE2bTgtOEg0XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkFkZCBibG9jazwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHsvKiBCbG9jayBUeXBlIFNlbGVjdG9yICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgcC0xXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE5IDlsLTcgNy03LTdcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgbXQtMSB3LTQ4IGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1tZCBzaGFkb3ctbGcgb3BhY2l0eS0wIGludmlzaWJsZSBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCBncm91cC1ob3Zlcjp2aXNpYmxlIHRyYW5zaXRpb24tYWxsIHotMTBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMVwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWRkQmxvY2soJ1RFWFQnKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgdGV4dC1sZWZ0IHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg8J+TnSBUZXh0XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWRkQmxvY2soJ0hFQURJTkcnKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgdGV4dC1sZWZ0IHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg8J+TsCBIZWFkaW5nXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWRkQmxvY2soJ0JVTExFVF9MSVNUJyl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIOKAoiBCdWxsZXQgTGlzdFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFkZEJsb2NrKCdOVU1CRVJFRF9MSVNUJyl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDEuIE51bWJlcmVkIExpc3RcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBZGRCbG9jaygnQ09ERScpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDwn5K7IENvZGVcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBZGRCbG9jaygnUVVPVEUnKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgdGV4dC1sZWZ0IHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg8J+SrCBRdW90ZVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgTm90ZUVkaXRvclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTm90ZXMiLCJCbG9ja0VkaXRvciIsIk5vdGVFZGl0b3IiLCJub3RlIiwiaW5pdGlhbE5vdGUiLCJzZWxlY3RlZE5vdGUiLCJ1cGRhdGVOb3RlIiwiY3JlYXRlQmxvY2siLCJsb2FkTm90ZSIsImxvYWRpbmdOb3RlIiwiaXNFZGl0aW5nVGl0bGUiLCJzZXRJc0VkaXRpbmdUaXRsZSIsInRpdGxlIiwic2V0VGl0bGUiLCJkZXNjcmlwdGlvbiIsInNldERlc2NyaXB0aW9uIiwiaWQiLCJoYW5kbGVTYXZlVGl0bGUiLCJ1bmRlZmluZWQiLCJlcnJvciIsImNvbnNvbGUiLCJhbGVydCIsImhhbmRsZUFkZEJsb2NrIiwidHlwZSIsIm5ld09yZGVyIiwiYmxvY2tzIiwibGVuZ3RoIiwiZm9ybWF0RGF0ZSIsImRhdGUiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsImRpdiIsImNsYXNzTmFtZSIsImlucHV0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsImF1dG9Gb2N1cyIsInRleHRhcmVhIiwicm93cyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMSIsInAiLCJzcGFuIiwiYXV0aG9yIiwibmFtZSIsInVzZXJuYW1lIiwidXBkYXRlZEF0IiwibWFwIiwiYmxvY2siLCJpbmRleCIsIm5vdGVJZCIsImlzTGFzdCIsIm9uQWRkQmxvY2siLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/notes/NoteEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/notes/NotesInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/notes/NotesInterface.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotesInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/NotesContext */ \"(app-pages-browser)/./src/contexts/NotesContext.tsx\");\n/* harmony import */ var _CreateNoteModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CreateNoteModal */ \"(app-pages-browser)/./src/components/notes/CreateNoteModal.tsx\");\n/* harmony import */ var _NoteEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoteEditor */ \"(app-pages-browser)/./src/components/notes/NoteEditor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NotesInterface(param) {\n    let { group } = param;\n    _s();\n    const { notes, selectedNote, loading, loadNotes, selectNote, deleteNote } = (0,_contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes)();\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNotesList, setShowNotesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotesInterface.useEffect\": ()=>{\n            if (group) {\n                loadNotes(group.id);\n            }\n        }\n    }[\"NotesInterface.useEffect\"], [\n        group.id\n    ]);\n    const handleDeleteNote = async (noteId)=>{\n        if (confirm('Are you sure you want to delete this note?')) {\n            try {\n                await deleteNote(noteId);\n            } catch (error) {\n                console.error('Failed to delete note:', error);\n                alert('Failed to delete note');\n            }\n        }\n    };\n    const formatDate = (date)=>{\n        return new Date(date).toLocaleDateString([], {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    if (selectedNote && !showNotesList) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white border-b border-gray-200 p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowNotesList(true),\n                        className: \"text-blue-500 hover:text-blue-600 text-sm mb-2\",\n                        children: \"← Back to Notes\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoteEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    note: selectedNote\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-gray-200 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Notes\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreateModal(true),\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\",\n                                    children: \"New Note\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-500\",\n                                    children: \"Loading notes...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this) : notes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-gray-300 mx-auto mb-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1,\n                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No Notes Yet\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-4\",\n                                    children: \"Create your first note to start collaborating\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreateModal(true),\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\",\n                                    children: \"Create Note\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                            children: notes.map((note)=>{\n                                var _note__count, _note_blocks;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200\",\n                                    onClick: ()=>{\n                                        selectNote(note);\n                                        setShowNotesList(false);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 truncate\",\n                                                        children: note.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteNote(note.id);\n                                                        },\n                                                        className: \"text-gray-400 hover:text-red-500 p-1\",\n                                                        title: \"Delete note\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 21\n                                            }, this),\n                                            note.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                children: note.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: note.author.name || note.author.username\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mx-1\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    ((_note__count = note._count) === null || _note__count === void 0 ? void 0 : _note__count.blocks) || ((_note_blocks = note.blocks) === null || _note_blocks === void 0 ? void 0 : _note_blocks.length) || 0,\n                                                                    \" blocks\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatDate(note.updatedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 19\n                                    }, this)\n                                }, note.id, false, {\n                                    fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateNoteModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showCreateModal,\n                onClose: ()=>setShowCreateModal(false),\n                groupId: group.id\n            }, void 0, false, {\n                fileName: \"/home/<USER>/mybinder/src/components/notes/NotesInterface.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NotesInterface, \"Bo+iFnUksTe9nG/PVYkMFKzR9VE=\", false, function() {\n    return [\n        _contexts_NotesContext__WEBPACK_IMPORTED_MODULE_2__.useNotes\n    ];\n});\n_c = NotesInterface;\nvar _c;\n$RefreshReg$(_c, \"NotesInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/notes/NotesInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const refreshUser = async ()=>{\n        try {\n            const response = await fetch('/api/auth/me');\n            if (response.ok) {\n                const data = await response.json();\n                setUser(data.user);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error('Failed to refresh user:', error);\n            setUser(null);\n        }\n    };\n    const login = async (email, password)=>{\n        const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Login failed');\n        }\n        const data = await response.json();\n        setUser(data.user);\n    };\n    const register = async (email, username, password, name)=>{\n        const response = await fetch('/api/auth/register', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                email,\n                username,\n                password,\n                name\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Registration failed');\n        }\n        // After successful registration, log the user in\n        await login(email, password);\n    };\n    const logout = async ()=>{\n        try {\n            await fetch('/api/auth/logout', {\n                method: 'POST'\n            });\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            refreshUser().finally({\n                \"AuthProvider.useEffect\": ()=>setLoading(false)\n            }[\"AuthProvider.useEffect\"]);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            login,\n            register,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/contexts/AuthContext.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/GroupContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/GroupContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupProvider: () => (/* binding */ GroupProvider),\n/* harmony export */   useGroups: () => (/* binding */ useGroups)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GroupProvider,useGroups auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst GroupContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction GroupProvider(param) {\n    let { children } = param;\n    _s();\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const refreshGroups = async ()=>{\n        try {\n            const response = await fetch('/api/groups');\n            if (response.ok) {\n                const data = await response.json();\n                setGroups(data.groups);\n            } else {\n                console.error('Failed to fetch groups');\n            }\n        } catch (error) {\n            console.error('Failed to refresh groups:', error);\n        }\n    };\n    const createGroup = async (data)=>{\n        const response = await fetch('/api/groups', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to create group');\n        }\n        const result = await response.json();\n        await refreshGroups();\n        return result.group;\n    };\n    const updateGroup = async (groupId, data)=>{\n        const response = await fetch(\"/api/groups/\".concat(groupId), {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to update group');\n        }\n        const result = await response.json();\n        await refreshGroups();\n        // Update selected group if it's the one being updated\n        if ((selectedGroup === null || selectedGroup === void 0 ? void 0 : selectedGroup.id) === groupId) {\n            setSelectedGroup(result.group);\n        }\n        return result.group;\n    };\n    const deleteGroup = async (groupId)=>{\n        const response = await fetch(\"/api/groups/\".concat(groupId), {\n            method: 'DELETE'\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to delete group');\n        }\n        // Clear selected group if it's the one being deleted\n        if ((selectedGroup === null || selectedGroup === void 0 ? void 0 : selectedGroup.id) === groupId) {\n            setSelectedGroup(null);\n        }\n        await refreshGroups();\n    };\n    const selectGroup = (group)=>{\n        setSelectedGroup(group);\n    };\n    const addMember = async function(groupId, email) {\n        let role = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'MEMBER';\n        const response = await fetch(\"/api/groups/\".concat(groupId, \"/members\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                email,\n                role\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to add member');\n        }\n        await refreshGroups();\n        // Refresh selected group if it's the one being updated\n        if ((selectedGroup === null || selectedGroup === void 0 ? void 0 : selectedGroup.id) === groupId) {\n            const groupResponse = await fetch(\"/api/groups/\".concat(groupId));\n            if (groupResponse.ok) {\n                const groupData = await groupResponse.json();\n                setSelectedGroup(groupData.group);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GroupProvider.useEffect\": ()=>{\n            refreshGroups().finally({\n                \"GroupProvider.useEffect\": ()=>setLoading(false)\n            }[\"GroupProvider.useEffect\"]);\n        }\n    }[\"GroupProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GroupContext.Provider, {\n        value: {\n            groups,\n            selectedGroup,\n            loading,\n            createGroup,\n            updateGroup,\n            deleteGroup,\n            selectGroup,\n            refreshGroups,\n            addMember\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/contexts/GroupContext.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupProvider, \"Ke/5d+gqyMoU48BidO7y9LqaA3g=\");\n_c = GroupProvider;\nfunction useGroups() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GroupContext);\n    if (context === undefined) {\n        throw new Error('useGroups must be used within a GroupProvider');\n    }\n    return context;\n}\n_s1(useGroups, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"GroupProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/GroupContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/MessageContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/MessageContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageProvider: () => (/* binding */ MessageProvider),\n/* harmony export */   useMessages: () => (/* binding */ useMessages)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MessageProvider,useMessages auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst MessageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MessageProvider(param) {\n    let { children } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentGroupId, setCurrentGroupId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadMessages = async function(groupId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        if (groupId !== currentGroupId) {\n            // Reset state when switching groups\n            setMessages([]);\n            setCurrentPage(1);\n            setCurrentGroupId(groupId);\n            page = 1;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/groups/\".concat(groupId, \"/messages?page=\").concat(page, \"&limit=50\"));\n            if (response.ok) {\n                const data = await response.json();\n                if (page === 1) {\n                    setMessages(data.messages);\n                } else {\n                    // Append older messages for pagination\n                    setMessages((prev)=>[\n                            ...data.messages,\n                            ...prev\n                        ]);\n                }\n                setHasMore(data.pagination.hasMore);\n                setCurrentPage(page);\n            } else {\n                console.error('Failed to load messages');\n            }\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const sendMessage = async (groupId, content)=>{\n        setSending(true);\n        try {\n            const response = await fetch(\"/api/groups/\".concat(groupId, \"/messages\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to send message');\n            }\n            const result = await response.json();\n            // Add new message to the end of the list\n            setMessages((prev)=>[\n                    ...prev,\n                    result.data\n                ]);\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            throw error;\n        } finally{\n            setSending(false);\n        }\n    };\n    const clearMessages = ()=>{\n        setMessages([]);\n        setCurrentPage(1);\n        setHasMore(false);\n        setCurrentGroupId(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContext.Provider, {\n        value: {\n            messages,\n            loading,\n            sending,\n            sendMessage,\n            loadMessages,\n            clearMessages,\n            hasMore,\n            currentPage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/contexts/MessageContext.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageProvider, \"z3/WUbEYZliCzBEc+qHtQaNnvjc=\");\n_c = MessageProvider;\nfunction useMessages() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MessageContext);\n    if (context === undefined) {\n        throw new Error('useMessages must be used within a MessageProvider');\n    }\n    return context;\n}\n_s1(useMessages, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"MessageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/MessageContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/NotesContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/NotesContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotesProvider: () => (/* binding */ NotesProvider),\n/* harmony export */   useNotes: () => (/* binding */ useNotes)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NotesProvider,useNotes auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst NotesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction NotesProvider(param) {\n    let { children } = param;\n    _s();\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNote, setSelectedNote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingNote, setLoadingNote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadNotes = async (groupId)=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/groups/\".concat(groupId, \"/notes\"));\n            if (response.ok) {\n                const data = await response.json();\n                setNotes(data.notes);\n            } else {\n                console.error('Failed to load notes');\n            }\n        } catch (error) {\n            console.error('Failed to load notes:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadNote = async (noteId)=>{\n        setLoadingNote(true);\n        try {\n            const response = await fetch(\"/api/notes/\".concat(noteId));\n            if (response.ok) {\n                const data = await response.json();\n                setSelectedNote(data.note);\n            } else {\n                console.error('Failed to load note');\n            }\n        } catch (error) {\n            console.error('Failed to load note:', error);\n        } finally{\n            setLoadingNote(false);\n        }\n    };\n    const createNote = async (groupId, data)=>{\n        const response = await fetch(\"/api/groups/\".concat(groupId, \"/notes\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to create note');\n        }\n        const result = await response.json();\n        await loadNotes(groupId);\n        return result.note;\n    };\n    const updateNote = async (noteId, data)=>{\n        const response = await fetch(\"/api/notes/\".concat(noteId), {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to update note');\n        }\n        const result = await response.json();\n        // Update selected note if it's the one being updated\n        if ((selectedNote === null || selectedNote === void 0 ? void 0 : selectedNote.id) === noteId) {\n            setSelectedNote(result.note);\n        }\n        // Update notes list\n        setNotes((prev)=>prev.map((note)=>note.id === noteId ? {\n                    ...note,\n                    ...data\n                } : note));\n        return result.note;\n    };\n    const deleteNote = async (noteId)=>{\n        const response = await fetch(\"/api/notes/\".concat(noteId), {\n            method: 'DELETE'\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to delete note');\n        }\n        // Clear selected note if it's the one being deleted\n        if ((selectedNote === null || selectedNote === void 0 ? void 0 : selectedNote.id) === noteId) {\n            setSelectedNote(null);\n        }\n        // Remove from notes list\n        setNotes((prev)=>prev.filter((note)=>note.id !== noteId));\n    };\n    const selectNote = (note)=>{\n        setSelectedNote(note);\n        // Load full note details with blocks if note is provided\n        if (note === null || note === void 0 ? void 0 : note.id) {\n            loadNote(note.id);\n        }\n    };\n    const clearNotes = ()=>{\n        setNotes([]);\n        setSelectedNote(null);\n    };\n    // Block operations\n    const createBlock = async (noteId, type, content, order)=>{\n        const response = await fetch(\"/api/notes/\".concat(noteId, \"/blocks\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                type,\n                content,\n                order\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to create block');\n        }\n        const result = await response.json();\n        // Refresh the selected note to get updated blocks\n        if ((selectedNote === null || selectedNote === void 0 ? void 0 : selectedNote.id) === noteId) {\n            await loadNote(noteId);\n        }\n        return result.block;\n    };\n    const updateBlock = async (blockId, data)=>{\n        const response = await fetch(\"/api/blocks/\".concat(blockId), {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to update block');\n        }\n        const result = await response.json();\n        // Update the block in selected note\n        if (selectedNote) {\n            setSelectedNote((prev)=>prev ? {\n                    ...prev,\n                    blocks: prev.blocks.map((block)=>block.id === blockId ? {\n                            ...block,\n                            ...data\n                        } : block)\n                } : null);\n        }\n        return result.block;\n    };\n    const deleteBlock = async (blockId)=>{\n        const response = await fetch(\"/api/blocks/\".concat(blockId), {\n            method: 'DELETE'\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to delete block');\n        }\n        // Remove block from selected note\n        if (selectedNote) {\n            setSelectedNote((prev)=>prev ? {\n                    ...prev,\n                    blocks: prev.blocks.filter((block)=>block.id !== blockId)\n                } : null);\n        }\n    };\n    const reorderBlocks = async (noteId, blocks)=>{\n        const response = await fetch(\"/api/notes/\".concat(noteId, \"/blocks\"), {\n            method: 'PUT',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                blocks\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || 'Failed to reorder blocks');\n        }\n        // Refresh the selected note to get updated order\n        if ((selectedNote === null || selectedNote === void 0 ? void 0 : selectedNote.id) === noteId) {\n            await loadNote(noteId);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotesContext.Provider, {\n        value: {\n            notes,\n            selectedNote,\n            loading,\n            loadingNote,\n            createNote,\n            updateNote,\n            deleteNote,\n            selectNote,\n            loadNotes,\n            loadNote,\n            clearNotes,\n            createBlock,\n            updateBlock,\n            deleteBlock,\n            reorderBlocks\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/mybinder/src/contexts/NotesContext.tsx\",\n        lineNumber: 241,\n        columnNumber: 5\n    }, this);\n}\n_s(NotesProvider, \"rKkyr5ZPNpNimVL1T8JORR5pk9s=\");\n_c = NotesProvider;\nfunction useNotes() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotesContext);\n    if (context === undefined) {\n        throw new Error('useNotes must be used within a NotesProvider');\n    }\n    return context;\n}\n_s1(useNotes, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"NotesProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/NotesContext.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fryan%2Fmybinder%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);