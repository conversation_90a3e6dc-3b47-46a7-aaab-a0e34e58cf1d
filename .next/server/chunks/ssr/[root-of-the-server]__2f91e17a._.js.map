{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/contexts/GroupContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { GroupWithMembers, CreateGroupData } from '@/types'\n\ninterface GroupContextType {\n  groups: GroupWithMembers[]\n  selectedGroup: GroupWithMembers | null\n  loading: boolean\n  createGroup: (data: CreateGroupData) => Promise<GroupWithMembers>\n  updateGroup: (groupId: string, data: Partial<CreateGroupData>) => Promise<GroupWithMembers>\n  deleteGroup: (groupId: string) => Promise<void>\n  selectGroup: (group: GroupWithMembers | null) => void\n  refreshGroups: () => Promise<void>\n  addMember: (groupId: string, email: string, role?: 'MEMBER' | 'ADMIN') => Promise<void>\n}\n\nconst GroupContext = createContext<GroupContextType | undefined>(undefined)\n\nexport function GroupProvider({ children }: { children: React.ReactNode }) {\n  const [groups, setGroups] = useState<GroupWithMembers[]>([])\n  const [selectedGroup, setSelectedGroup] = useState<GroupWithMembers | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  const refreshGroups = async () => {\n    try {\n      const response = await fetch('/api/groups')\n      if (response.ok) {\n        const data = await response.json()\n        setGroups(data.groups)\n      } else {\n        console.error('Failed to fetch groups')\n      }\n    } catch (error) {\n      console.error('Failed to refresh groups:', error)\n    }\n  }\n\n  const createGroup = async (data: CreateGroupData): Promise<GroupWithMembers> => {\n    const response = await fetch('/api/groups', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to create group')\n    }\n\n    const result = await response.json()\n    await refreshGroups()\n    return result.group\n  }\n\n  const updateGroup = async (groupId: string, data: Partial<CreateGroupData>): Promise<GroupWithMembers> => {\n    const response = await fetch(`/api/groups/${groupId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to update group')\n    }\n\n    const result = await response.json()\n    await refreshGroups()\n    \n    // Update selected group if it's the one being updated\n    if (selectedGroup?.id === groupId) {\n      setSelectedGroup(result.group)\n    }\n    \n    return result.group\n  }\n\n  const deleteGroup = async (groupId: string): Promise<void> => {\n    const response = await fetch(`/api/groups/${groupId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to delete group')\n    }\n\n    // Clear selected group if it's the one being deleted\n    if (selectedGroup?.id === groupId) {\n      setSelectedGroup(null)\n    }\n    \n    await refreshGroups()\n  }\n\n  const selectGroup = (group: GroupWithMembers | null) => {\n    setSelectedGroup(group)\n  }\n\n  const addMember = async (groupId: string, email: string, role: 'MEMBER' | 'ADMIN' = 'MEMBER'): Promise<void> => {\n    const response = await fetch(`/api/groups/${groupId}/members`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, role }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to add member')\n    }\n\n    await refreshGroups()\n    \n    // Refresh selected group if it's the one being updated\n    if (selectedGroup?.id === groupId) {\n      const groupResponse = await fetch(`/api/groups/${groupId}`)\n      if (groupResponse.ok) {\n        const groupData = await groupResponse.json()\n        setSelectedGroup(groupData.group)\n      }\n    }\n  }\n\n  useEffect(() => {\n    refreshGroups().finally(() => setLoading(false))\n  }, [])\n\n  return (\n    <GroupContext.Provider value={{\n      groups,\n      selectedGroup,\n      loading,\n      createGroup,\n      updateGroup,\n      deleteGroup,\n      selectGroup,\n      refreshGroups,\n      addMember,\n    }}>\n      {children}\n    </GroupContext.Provider>\n  )\n}\n\nexport function useGroups() {\n  const context = useContext(GroupContext)\n  if (context === undefined) {\n    throw new Error('useGroups must be used within a GroupProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAiBA,MAAM,6BAAe,IAAA,sNAAa,EAA+B;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAqB,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAA0B;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,MAAM;YACvB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,MAAM;QACN,OAAO,OAAO,KAAK;IACrB;IAEA,MAAM,cAAc,OAAO,SAAiB;QAC1C,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;YACrD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,MAAM;QAEN,sDAAsD;QACtD,IAAI,eAAe,OAAO,SAAS;YACjC,iBAAiB,OAAO,KAAK;QAC/B;QAEA,OAAO,OAAO,KAAK;IACrB;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;YACrD,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,qDAAqD;QACrD,IAAI,eAAe,OAAO,SAAS;YACjC,iBAAiB;QACnB;QAEA,MAAM;IACR;IAEA,MAAM,cAAc,CAAC;QACnB,iBAAiB;IACnB;IAEA,MAAM,YAAY,OAAO,SAAiB,OAAe,OAA2B,QAAQ;QAC1F,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,QAAQ,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAK;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM;QAEN,uDAAuD;QACvD,IAAI,eAAe,OAAO,SAAS;YACjC,MAAM,gBAAgB,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS;YAC1D,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,iBAAiB,UAAU,KAAK;YAClC;QACF;IACF;IAEA,IAAA,kNAAS,EAAC;QACR,gBAAgB,OAAO,CAAC,IAAM,WAAW;IAC3C,GAAG,EAAE;IAEL,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAC5B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/components/groups/CreateGroupModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useGroups } from '@/contexts/GroupContext'\n\ninterface CreateGroupModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function CreateGroupModal({ isOpen, onClose }: CreateGroupModalProps) {\n  const [name, setName] = useState('')\n  const [description, setDescription] = useState('')\n  const [isPrivate, setIsPrivate] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const { createGroup } = useGroups()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await createGroup({\n        name,\n        description: description || undefined,\n        isPrivate,\n      })\n      \n      // Reset form and close modal\n      setName('')\n      setDescription('')\n      setIsPrivate(false)\n      onClose()\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create group')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setName('')\n      setDescription('')\n      setIsPrivate(false)\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-900\">Create New Group</h2>\n          <button\n            onClick={handleClose}\n            disabled={loading}\n            className=\"text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"name\">\n              Group Name *\n            </label>\n            <input\n              id=\"name\"\n              type=\"text\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter group name\"\n              required\n              maxLength={100}\n            />\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-gray-700 text-sm font-bold mb-2\" htmlFor=\"description\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n              placeholder=\"Enter group description (optional)\"\n              rows={3}\n              maxLength={500}\n            />\n          </div>\n\n          <div className=\"mb-6\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={isPrivate}\n                onChange={(e) => setIsPrivate(e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-gray-700 text-sm\">Private Group</span>\n            </label>\n            <p className=\"text-xs text-gray-500 mt-1\">\n              Private groups require invitation to join\n            </p>\n          </div>\n\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              disabled={loading}\n              className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || !name.trim()}\n              className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Creating...' : 'Create Group'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAyB;IACjF,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAC;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,EAAE,WAAW,EAAE,GAAG,IAAA,6IAAS;IAEjC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,YAAY;gBAChB;gBACA,aAAa,eAAe;gBAC5B;YACF;YAEA,6BAA6B;YAC7B,QAAQ;YACR,eAAe;YACf,aAAa;YACb;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,eAAe;YACf,aAAa;YACb,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkC;;;;;;sCAChD,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAK1E,uBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,8OAAC;oBAAK,UAAU;;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;oCAA6C,SAAQ;8CAAO;;;;;;8CAG7E,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,WAAU;oCACV,aAAY;oCACZ,QAAQ;oCACR,WAAW;;;;;;;;;;;;sCAIf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;oCAA6C,SAAQ;8CAAc;;;;;;8CAGpF,8OAAC;oCACC,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,aAAY;oCACZ,MAAM;oCACN,WAAW;;;;;;;;;;;;sCAIf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;4CAC9C,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU,WAAW,CAAC,KAAK,IAAI;oCAC/B,WAAU;8CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/components/groups/GroupList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useGroups } from '@/contexts/GroupContext'\nimport { useAuth } from '@/contexts/AuthContext'\nimport CreateGroupModal from './CreateGroupModal'\n\nexport default function GroupList() {\n  const { groups, selectedGroup, selectGroup, loading } = useGroups()\n  const { user } = useAuth()\n  const [showCreateModal, setShowCreateModal] = useState(false)\n\n  if (loading) {\n    return (\n      <div className=\"w-80 bg-white border-r border-gray-200 p-4\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-4\"></div>\n          <div className=\"space-y-3\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"w-80 bg-white border-r border-gray-200 flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Groups</h2>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full\"\n              title=\"Create new group\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Groups List */}\n        <div className=\"flex-1 overflow-y-auto\">\n          {groups.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              <p className=\"mb-2\">No groups yet</p>\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"text-blue-500 hover:text-blue-600 text-sm\"\n              >\n                Create your first group\n              </button>\n            </div>\n          ) : (\n            <div className=\"p-2\">\n              {groups.map((group) => (\n                <div\n                  key={group.id}\n                  onClick={() => selectGroup(group)}\n                  className={`p-3 rounded-lg cursor-pointer mb-2 transition-colors ${\n                    selectedGroup?.id === group.id\n                      ? 'bg-blue-50 border border-blue-200'\n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center\">\n                        <h3 className=\"text-sm font-medium text-gray-900 truncate\">\n                          {group.name}\n                        </h3>\n                        {group.isPrivate && (\n                          <svg className=\"w-3 h-3 text-gray-400 ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                            <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\" />\n                          </svg>\n                        )}\n                      </div>\n                      {group.description && (\n                        <p className=\"text-xs text-gray-500 truncate mt-1\">\n                          {group.description}\n                        </p>\n                      )}\n                      <div className=\"flex items-center mt-2 text-xs text-gray-400\">\n                        <span>{group._count.members} members</span>\n                        <span className=\"mx-1\">•</span>\n                        <span>{group._count.messages} messages</span>\n                        <span className=\"mx-1\">•</span>\n                        <span>{group._count.notes} notes</span>\n                      </div>\n                    </div>\n                    {group.ownerId === user?.id && (\n                      <div className=\"ml-2\">\n                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                          Owner\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      <CreateGroupModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAS;IACjE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,0IAAO;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAC;IAEvD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7E,8OAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,KAAK,kBACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;;;;;;iDAKH,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oCAEC,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,qDAAqD,EAC/D,eAAe,OAAO,MAAM,EAAE,GAC1B,sCACA,oBACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,MAAM,IAAI;;;;;;4DAEZ,MAAM,SAAS,kBACd,8OAAC;gEAAI,WAAU;gEAA6B,MAAK;gEAAe,SAAQ;0EACtE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAyG,UAAS;;;;;;;;;;;;;;;;;oDAIlJ,MAAM,WAAW,kBAChB,8OAAC;wDAAE,WAAU;kEACV,MAAM,WAAW;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAM,MAAM,MAAM,CAAC,OAAO;oEAAC;;;;;;;0EAC5B,8OAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,8OAAC;;oEAAM,MAAM,MAAM,CAAC,QAAQ;oEAAC;;;;;;;0EAC7B,8OAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,8OAAC;;oEAAM,MAAM,MAAM,CAAC,KAAK;oEAAC;;;;;;;;;;;;;;;;;;;4CAG7B,MAAM,OAAO,KAAK,MAAM,oBACvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAkG;;;;;;;;;;;;;;;;;mCAnCnH,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BAgDzB,8OAAC,2JAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/contexts/MessageContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { MessageWithAuthor } from '@/types'\n\ninterface MessageContextType {\n  messages: MessageWithAuthor[]\n  loading: boolean\n  sending: boolean\n  sendMessage: (groupId: string, content: string) => Promise<void>\n  loadMessages: (groupId: string, page?: number) => Promise<void>\n  clearMessages: () => void\n  hasMore: boolean\n  currentPage: number\n}\n\nconst MessageContext = createContext<MessageContextType | undefined>(undefined)\n\nexport function MessageProvider({ children }: { children: React.ReactNode }) {\n  const [messages, setMessages] = useState<MessageWithAuthor[]>([])\n  const [loading, setLoading] = useState(false)\n  const [sending, setSending] = useState(false)\n  const [hasMore, setHasMore] = useState(false)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [currentGroupId, setCurrentGroupId] = useState<string | null>(null)\n\n  const loadMessages = async (groupId: string, page: number = 1) => {\n    if (groupId !== currentGroupId) {\n      // Reset state when switching groups\n      setMessages([])\n      setCurrentPage(1)\n      setCurrentGroupId(groupId)\n      page = 1\n    }\n\n    setLoading(true)\n    try {\n      const response = await fetch(`/api/groups/${groupId}/messages?page=${page}&limit=50`)\n      if (response.ok) {\n        const data = await response.json()\n        \n        if (page === 1) {\n          setMessages(data.messages)\n        } else {\n          // Append older messages for pagination\n          setMessages(prev => [...data.messages, ...prev])\n        }\n        \n        setHasMore(data.pagination.hasMore)\n        setCurrentPage(page)\n      } else {\n        console.error('Failed to load messages')\n      }\n    } catch (error) {\n      console.error('Failed to load messages:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const sendMessage = async (groupId: string, content: string) => {\n    setSending(true)\n    try {\n      const response = await fetch(`/api/groups/${groupId}/messages`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ content }),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to send message')\n      }\n\n      const result = await response.json()\n      \n      // Add new message to the end of the list\n      setMessages(prev => [...prev, result.data])\n    } catch (error) {\n      console.error('Failed to send message:', error)\n      throw error\n    } finally {\n      setSending(false)\n    }\n  }\n\n  const clearMessages = () => {\n    setMessages([])\n    setCurrentPage(1)\n    setHasMore(false)\n    setCurrentGroupId(null)\n  }\n\n  return (\n    <MessageContext.Provider value={{\n      messages,\n      loading,\n      sending,\n      sendMessage,\n      loadMessages,\n      clearMessages,\n      hasMore,\n      currentPage,\n    }}>\n      {children}\n    </MessageContext.Provider>\n  )\n}\n\nexport function useMessages() {\n  const context = useContext(MessageContext)\n  if (context === undefined) {\n    throw new Error('useMessages must be used within a MessageProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAgBA,MAAM,+BAAiB,IAAA,sNAAa,EAAiC;AAE9D,SAAS,gBAAgB,EAAE,QAAQ,EAAiC;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAsB,EAAE;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAgB;IAEpE,MAAM,eAAe,OAAO,SAAiB,OAAe,CAAC;QAC3D,IAAI,YAAY,gBAAgB;YAC9B,oCAAoC;YACpC,YAAY,EAAE;YACd,eAAe;YACf,kBAAkB;YAClB,OAAO;QACT;QAEA,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,eAAe,EAAE,KAAK,SAAS,CAAC;YACpF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,GAAG;oBACd,YAAY,KAAK,QAAQ;gBAC3B,OAAO;oBACL,uCAAuC;oBACvC,YAAY,CAAA,OAAQ;+BAAI,KAAK,QAAQ;+BAAK;yBAAK;gBACjD;gBAEA,WAAW,KAAK,UAAU,CAAC,OAAO;gBAClC,eAAe;YACjB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,OAAO,SAAiB;QAC1C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,SAAS,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,yCAAyC;YACzC,YAAY,CAAA,OAAQ;uBAAI;oBAAM,OAAO,IAAI;iBAAC;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY,EAAE;QACd,eAAe;QACf,WAAW;QACX,kBAAkB;IACpB;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAC9B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/contexts/NotesContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState } from 'react'\nimport { NoteWithBlocks, CreateNoteData, NoteBlockWithAuthor, BlockType } from '@/types'\n\ninterface NotesContextType {\n  notes: NoteWithBlocks[]\n  selectedNote: NoteWithBlocks | null\n  loading: boolean\n  loadingNote: boolean\n  createNote: (groupId: string, data: CreateNoteData) => Promise<NoteWithBlocks>\n  updateNote: (noteId: string, data: Partial<CreateNoteData>) => Promise<NoteWithBlocks>\n  deleteNote: (noteId: string) => Promise<void>\n  selectNote: (note: NoteWithBlocks | null) => void\n  loadNotes: (groupId: string) => Promise<void>\n  loadNote: (noteId: string) => Promise<void>\n  clearNotes: () => void\n  // Block operations\n  createBlock: (noteId: string, type: string, content: string, order: number) => Promise<NoteBlockWithAuthor>\n  updateBlock: (blockId: string, data: { content?: string; type?: BlockType }) => Promise<NoteBlockWithAuthor>\n  deleteBlock: (blockId: string) => Promise<void>\n  reorderBlocks: (noteId: string, blocks: { id: string; order: number }[]) => Promise<void>\n}\n\nconst NotesContext = createContext<NotesContextType | undefined>(undefined)\n\nexport function NotesProvider({ children }: { children: React.ReactNode }) {\n  const [notes, setNotes] = useState<NoteWithBlocks[]>([])\n  const [selectedNote, setSelectedNote] = useState<NoteWithBlocks | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [loadingNote, setLoadingNote] = useState(false)\n\n  const loadNotes = async (groupId: string) => {\n    setLoading(true)\n    try {\n      const response = await fetch(`/api/groups/${groupId}/notes`)\n      if (response.ok) {\n        const data = await response.json()\n        setNotes(data.notes)\n      } else {\n        console.error('Failed to load notes')\n      }\n    } catch (error) {\n      console.error('Failed to load notes:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadNote = async (noteId: string) => {\n    setLoadingNote(true)\n    try {\n      const response = await fetch(`/api/notes/${noteId}`)\n      if (response.ok) {\n        const data = await response.json()\n        setSelectedNote(data.note)\n      } else {\n        console.error('Failed to load note')\n      }\n    } catch (error) {\n      console.error('Failed to load note:', error)\n    } finally {\n      setLoadingNote(false)\n    }\n  }\n\n  const createNote = async (groupId: string, data: CreateNoteData): Promise<NoteWithBlocks> => {\n    const response = await fetch(`/api/groups/${groupId}/notes`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to create note')\n    }\n\n    const result = await response.json()\n    await loadNotes(groupId)\n    return result.note\n  }\n\n  const updateNote = async (noteId: string, data: Partial<CreateNoteData>): Promise<NoteWithBlocks> => {\n    const response = await fetch(`/api/notes/${noteId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to update note')\n    }\n\n    const result = await response.json()\n    \n    // Update selected note if it's the one being updated\n    if (selectedNote?.id === noteId) {\n      setSelectedNote(result.note)\n    }\n    \n    // Update notes list\n    setNotes(prev => prev.map(note => \n      note.id === noteId ? { ...note, ...data } : note\n    ))\n    \n    return result.note\n  }\n\n  const deleteNote = async (noteId: string): Promise<void> => {\n    const response = await fetch(`/api/notes/${noteId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to delete note')\n    }\n\n    // Clear selected note if it's the one being deleted\n    if (selectedNote?.id === noteId) {\n      setSelectedNote(null)\n    }\n    \n    // Remove from notes list\n    setNotes(prev => prev.filter(note => note.id !== noteId))\n  }\n\n  const selectNote = (note: NoteWithBlocks | null) => {\n    setSelectedNote(note)\n    // Load full note details with blocks if note is provided\n    if (note?.id) {\n      loadNote(note.id)\n    }\n  }\n\n  const clearNotes = () => {\n    setNotes([])\n    setSelectedNote(null)\n  }\n\n  // Block operations\n  const createBlock = async (noteId: string, type: string, content: string, order: number): Promise<NoteBlockWithAuthor> => {\n    const response = await fetch(`/api/notes/${noteId}/blocks`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ type, content, order }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to create block')\n    }\n\n    const result = await response.json()\n    \n    // Refresh the selected note to get updated blocks\n    if (selectedNote?.id === noteId) {\n      await loadNote(noteId)\n    }\n    \n    return result.block\n  }\n\n  const updateBlock = async (blockId: string, data: { content?: string; type?: BlockType }): Promise<NoteBlockWithAuthor> => {\n    const response = await fetch(`/api/blocks/${blockId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to update block')\n    }\n\n    const result = await response.json()\n    \n    // Update the block in selected note\n    if (selectedNote) {\n      setSelectedNote(prev => prev ? {\n        ...prev,\n        blocks: prev.blocks.map(block => \n          block.id === blockId ? { ...block, ...data } : block\n        )\n      } : null)\n    }\n    \n    return result.block\n  }\n\n  const deleteBlock = async (blockId: string): Promise<void> => {\n    const response = await fetch(`/api/blocks/${blockId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to delete block')\n    }\n\n    // Remove block from selected note\n    if (selectedNote) {\n      setSelectedNote(prev => prev ? {\n        ...prev,\n        blocks: prev.blocks.filter(block => block.id !== blockId)\n      } : null)\n    }\n  }\n\n  const reorderBlocks = async (noteId: string, blocks: { id: string; order: number }[]): Promise<void> => {\n    const response = await fetch(`/api/notes/${noteId}/blocks`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ blocks }),\n    })\n\n    if (!response.ok) {\n      const error = await response.json()\n      throw new Error(error.error || 'Failed to reorder blocks')\n    }\n\n    // Refresh the selected note to get updated order\n    if (selectedNote?.id === noteId) {\n      await loadNote(noteId)\n    }\n  }\n\n  return (\n    <NotesContext.Provider value={{\n      notes,\n      selectedNote,\n      loading,\n      loadingNote,\n      createNote,\n      updateNote,\n      deleteNote,\n      selectNote,\n      loadNotes,\n      loadNote,\n      clearNotes,\n      createBlock,\n      updateBlock,\n      deleteBlock,\n      reorderBlocks,\n    }}>\n      {children}\n    </NotesContext.Provider>\n  )\n}\n\nexport function useNotes() {\n  const context = useContext(NotesContext)\n  if (context === undefined) {\n    throw new Error('useNotes must be used within a NotesProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAwBA,MAAM,6BAAe,IAAA,sNAAa,EAA+B;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAmB,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAwB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAE/C,MAAM,YAAY,OAAO;QACvB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC;YAC3D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;YACnD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB,KAAK,IAAI;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,OAAO,SAAiB;QACzC,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,MAAM,UAAU;QAChB,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,aAAa,OAAO,QAAgB;QACxC,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,qDAAqD;QACrD,IAAI,cAAc,OAAO,QAAQ;YAC/B,gBAAgB,OAAO,IAAI;QAC7B;QAEA,oBAAoB;QACpB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,GAAG,IAAI;gBAAC,IAAI;QAG9C,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;YACnD,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,oDAAoD;QACpD,IAAI,cAAc,OAAO,QAAQ;YAC/B,gBAAgB;QAClB;QAEA,yBAAyB;QACzB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACnD;IAEA,MAAM,aAAa,CAAC;QAClB,gBAAgB;QAChB,yDAAyD;QACzD,IAAI,MAAM,IAAI;YACZ,SAAS,KAAK,EAAE;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,SAAS,EAAE;QACX,gBAAgB;IAClB;IAEA,mBAAmB;IACnB,MAAM,cAAc,OAAO,QAAgB,MAAc,SAAiB;QACxE,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAM;gBAAS;YAAM;QAC9C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,kDAAkD;QAClD,IAAI,cAAc,OAAO,QAAQ;YAC/B,MAAM,SAAS;QACjB;QAEA,OAAO,OAAO,KAAK;IACrB;IAEA,MAAM,cAAc,OAAO,SAAiB;QAC1C,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;YACrD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,oCAAoC;QACpC,IAAI,cAAc;YAChB,gBAAgB,CAAA,OAAQ,OAAO;oBAC7B,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,QACtB,MAAM,EAAE,KAAK,UAAU;4BAAE,GAAG,KAAK;4BAAE,GAAG,IAAI;wBAAC,IAAI;gBAEnD,IAAI;QACN;QAEA,OAAO,OAAO,KAAK;IACrB;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;YACrD,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,kCAAkC;QAClC,IAAI,cAAc;YAChB,gBAAgB,CAAA,OAAQ,OAAO;oBAC7B,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;gBACnD,IAAI;QACN;IACF;IAEA,MAAM,gBAAgB,OAAO,QAAgB;QAC3C,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;QACjC;QAEA,iDAAiD;QACjD,IAAI,cAAc,OAAO,QAAQ;YAC/B,MAAM,SAAS;QACjB;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAC5B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/components/messages/ChatInterface.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useMessages } from '@/contexts/MessageContext'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { GroupWithMembers } from '@/types'\n\ninterface ChatInterfaceProps {\n  group: GroupWithMembers\n}\n\nexport default function ChatInterface({ group }: ChatInterfaceProps) {\n  const { messages, loading, sending, sendMessage, loadMessages, hasMore, currentPage } = useMessages()\n  const { user } = useAuth()\n  const [newMessage, setNewMessage] = useState('')\n  const [error, setError] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const messagesContainerRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    if (group) {\n      loadMessages(group.id)\n    }\n  }, [group.id])\n\n  useEffect(() => {\n    // Scroll to bottom when new messages arrive\n    scrollToBottom()\n  }, [messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newMessage.trim() || sending) return\n\n    setError('')\n    const messageContent = newMessage.trim()\n    setNewMessage('')\n\n    try {\n      await sendMessage(group.id, messageContent)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to send message')\n      setNewMessage(messageContent) // Restore message on error\n    }\n  }\n\n  const loadMoreMessages = async () => {\n    if (hasMore && !loading) {\n      await loadMessages(group.id, currentPage + 1)\n    }\n  }\n\n  const formatTime = (date: string | Date) => {\n    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n  }\n\n  const formatDate = (date: string | Date) => {\n    const messageDate = new Date(date)\n    const today = new Date()\n    const yesterday = new Date(today)\n    yesterday.setDate(yesterday.getDate() - 1)\n\n    if (messageDate.toDateString() === today.toDateString()) {\n      return 'Today'\n    } else if (messageDate.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday'\n    } else {\n      return messageDate.toLocaleDateString()\n    }\n  }\n\n  const shouldShowDateSeparator = (currentMessage: any, previousMessage: any) => {\n    if (!previousMessage) return true\n    \n    const currentDate = new Date(currentMessage.createdAt).toDateString()\n    const previousDate = new Date(previousMessage.createdAt).toDateString()\n    \n    return currentDate !== previousDate\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Messages Container */}\n      <div \n        ref={messagesContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4\"\n      >\n        {/* Load More Button */}\n        {hasMore && (\n          <div className=\"text-center\">\n            <button\n              onClick={loadMoreMessages}\n              disabled={loading}\n              className=\"text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50\"\n            >\n              {loading ? 'Loading...' : 'Load older messages'}\n            </button>\n          </div>\n        )}\n\n        {/* Messages */}\n        {messages.map((message, index) => {\n          const previousMessage = index > 0 ? messages[index - 1] : null\n          const showDateSeparator = shouldShowDateSeparator(message, previousMessage)\n          const isOwnMessage = message.authorId === user?.id\n\n          return (\n            <div key={message.id}>\n              {/* Date Separator */}\n              {showDateSeparator && (\n                <div className=\"flex items-center justify-center my-4\">\n                  <div className=\"bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full\">\n                    {formatDate(message.createdAt)}\n                  </div>\n                </div>\n              )}\n\n              {/* Message */}\n              <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>\n                <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>\n                  {!isOwnMessage && (\n                    <div className=\"text-xs text-gray-700 mb-1 font-medium\">\n                      {message.author.name || message.author.username}\n                    </div>\n                  )}\n                  <div\n                    className={`px-4 py-2 rounded-lg ${\n                      isOwnMessage\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-900 border border-gray-200'\n                    }`}\n                  >\n                    <div className=\"text-sm whitespace-pre-wrap\">\n                      {message.content.includes('```') ? (\n                        <div>\n                          {message.content.split('```').map((part, index) =>\n                            index % 2 === 0 ? (\n                              <span key={index}>{part}</span>\n                            ) : (\n                              <code key={index} className=\"block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto\">\n                                {part}\n                              </code>\n                            )\n                          )}\n                        </div>\n                      ) : (\n                        message.content\n                      )}\n                    </div>\n                    <div className={`text-xs mt-1 ${isOwnMessage ? 'text-blue-100' : 'text-gray-600'}`}>\n                      {formatTime(message.createdAt)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n\n        {/* Loading indicator */}\n        {loading && messages.length === 0 && (\n          <div className=\"text-center text-gray-500\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\n            <p className=\"mt-2\">Loading messages...</p>\n          </div>\n        )}\n\n        {/* Empty state */}\n        {!loading && messages.length === 0 && (\n          <div className=\"text-center text-gray-500 py-8\">\n            <p>No messages yet. Start the conversation!</p>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message Input */}\n      <div className=\"border-t border-gray-200 p-4\">\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm\">\n            {error}\n          </div>\n        )}\n        \n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            placeholder=\"Type a message...\"\n            className=\"flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500\"\n            disabled={sending}\n            maxLength={2000}\n          />\n          <button\n            type=\"submit\"\n            disabled={!newMessage.trim() || sending}\n            className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {sending ? 'Sending...' : 'Send'}\n          </button>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,cAAc,EAAE,KAAK,EAAsB;IACjE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAA,iJAAW;IACnG,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,0IAAO;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,iBAAiB,IAAA,+MAAM,EAAiB;IAC9C,MAAM,uBAAuB,IAAA,+MAAM,EAAiB;IAEpD,IAAA,kNAAS,EAAC;QACR,IAAI,OAAO;YACT,aAAa,MAAM,EAAE;QACvB;IACF,GAAG;QAAC,MAAM,EAAE;KAAC;IAEb,IAAA,kNAAS,EAAC;QACR,4CAA4C;QAC5C;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,SAAS;QAEnC,SAAS;QACT,MAAM,iBAAiB,WAAW,IAAI;QACtC,cAAc;QAEd,IAAI;YACF,MAAM,YAAY,MAAM,EAAE,EAAE;QAC9B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,cAAc,iBAAgB,2BAA2B;QAC3D;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,CAAC,SAAS;YACvB,MAAM,aAAa,MAAM,EAAE,EAAE,cAAc;QAC7C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IACpF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,IAAI,KAAK;QAC7B,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,IAAI,YAAY,YAAY,OAAO,MAAM,YAAY,IAAI;YACvD,OAAO;QACT,OAAO,IAAI,YAAY,YAAY,OAAO,UAAU,YAAY,IAAI;YAClE,OAAO;QACT,OAAO;YACL,OAAO,YAAY,kBAAkB;QACvC;IACF;IAEA,MAAM,0BAA0B,CAAC,gBAAqB;QACpD,IAAI,CAAC,iBAAiB,OAAO;QAE7B,MAAM,cAAc,IAAI,KAAK,eAAe,SAAS,EAAE,YAAY;QACnE,MAAM,eAAe,IAAI,KAAK,gBAAgB,SAAS,EAAE,YAAY;QAErE,OAAO,gBAAgB;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,KAAK;gBACL,WAAU;;oBAGT,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,eAAe;;;;;;;;;;;oBAM/B,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,kBAAkB,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE,GAAG;wBAC1D,MAAM,oBAAoB,wBAAwB,SAAS;wBAC3D,MAAM,eAAe,QAAQ,QAAQ,KAAK,MAAM;wBAEhD,qBACE,8OAAC;;gCAEE,mCACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,WAAW,QAAQ,SAAS;;;;;;;;;;;8CAMnC,8OAAC;oCAAI,WAAW,CAAC,KAAK,EAAE,eAAe,gBAAgB,iBAAiB;8CACtE,cAAA,8OAAC;wCAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,YAAY,WAAW;;4CAC3E,CAAC,8BACA,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM,CAAC,IAAI,IAAI,QAAQ,MAAM,CAAC,QAAQ;;;;;;0DAGnD,8OAAC;gDACC,WAAW,CAAC,qBAAqB,EAC/B,eACI,2BACA,oDACJ;;kEAEF,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,OAAO,CAAC,QAAQ,CAAC,uBACxB,8OAAC;sEACE,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,QACvC,QAAQ,MAAM,kBACZ,8OAAC;8EAAkB;mEAAR;;;;yFAEX,8OAAC;oEAAiB,WAAU;8EACzB;mEADQ;;;;;;;;;mEAOjB,QAAQ,OAAO;;;;;;kEAGnB,8OAAC;wDAAI,WAAW,CAAC,aAAa,EAAE,eAAe,kBAAkB,iBAAiB;kEAC/E,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;2BA3C7B,QAAQ,EAAE;;;;;oBAkDxB;oBAGC,WAAW,SAAS,MAAM,KAAK,mBAC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;oBAKvB,CAAC,WAAW,SAAS,MAAM,KAAK,mBAC/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;kCAIP,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,8OAAC;wBAAK,UAAU;wBAAmB,WAAU;;0CAC3C,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,aAAY;gCACZ,WAAU;gCACV,UAAU;gCACV,WAAW;;;;;;0CAEb,8OAAC;gCACC,MAAK;gCACL,UAAU,CAAC,WAAW,IAAI,MAAM;gCAChC,WAAU;0CAET,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/components/notes/NotesInterface.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotes } from '@/contexts/NotesContext'\nimport { GroupWithMembers } from '@/types'\n// import CreateNoteModal from './CreateNoteModal'\n// import NoteEditor from './NoteEditor'\n\ninterface NotesInterfaceProps {\n  group: GroupWithMembers\n}\n\nexport default function NotesInterface({ group }: NotesInterfaceProps) {\n  const { notes, selectedNote, loading, loadNotes, selectNote, deleteNote } = useNotes()\n  const [showCreateModal, setShowCreateModal] = useState(false)\n  const [showNotesList, setShowNotesList] = useState(true)\n\n  useEffect(() => {\n    if (group) {\n      loadNotes(group.id)\n    }\n  }, [group.id])\n\n  const handleDeleteNote = async (noteId: string) => {\n    if (confirm('Are you sure you want to delete this note?')) {\n      try {\n        await deleteNote(noteId)\n      } catch (error) {\n        console.error('Failed to delete note:', error)\n        alert('Failed to delete note')\n      }\n    }\n  }\n\n  const formatDate = (date: string | Date) => {\n    return new Date(date).toLocaleDateString([], {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  if (selectedNote && !showNotesList) {\n    return (\n      <div className=\"flex-1 flex flex-col\">\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <button\n            onClick={() => setShowNotesList(true)}\n            className=\"text-blue-500 hover:text-blue-600 text-sm mb-2\"\n          >\n            ← Back to Notes\n          </button>\n        </div>\n        <NoteEditor note={selectedNote} />\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"flex-1 flex flex-col\">\n        {/* Header */}\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Notes</h2>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\"\n            >\n              New Note\n            </button>\n          </div>\n        </div>\n\n        {/* Notes List */}\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\n              <p className=\"mt-2 text-gray-500\">Loading notes...</p>\n            </div>\n          ) : notes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Notes Yet</h3>\n              <p className=\"text-gray-500 mb-4\">Create your first note to start collaborating</p>\n              <button\n                onClick={() => setShowCreateModal(true)}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\"\n              >\n                Create Note\n              </button>\n            </div>\n          ) : (\n            <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n              {notes.map((note) => (\n                <div\n                  key={note.id}\n                  className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200\"\n                  onClick={() => {\n                    selectNote(note)\n                    setShowNotesList(false)\n                  }}\n                >\n                  <div className=\"p-4\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                        {note.title}\n                      </h3>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          handleDeleteNote(note.id)\n                        }}\n                        className=\"text-gray-400 hover:text-red-500 p-1\"\n                        title=\"Delete note\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                      </button>\n                    </div>\n                    \n                    {note.description && (\n                      <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                        {note.description}\n                      </p>\n                    )}\n                    \n                    <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <span className=\"font-medium\">\n                          {note.author.name || note.author.username}\n                        </span>\n                        <span className=\"mx-1\">•</span>\n                        <span>{note._count?.blocks || note.blocks?.length || 0} blocks</span>\n                      </div>\n                      <span>{formatDate(note.updatedAt)}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      <CreateNoteModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        groupId={group.id}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYe,SAAS,eAAe,EAAE,KAAK,EAAuB;IACnE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,IAAA,4IAAQ;IACpF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IAEnD,IAAA,kNAAS,EAAC;QACR,IAAI,OAAO;YACT,UAAU,MAAM,EAAE;QACpB;IACF,GAAG;QAAC,MAAM,EAAE;KAAC;IAEb,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,+CAA+C;YACzD,IAAI;gBACF,MAAM,WAAW;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM;YACR;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,EAAE,EAAE;YAC3C,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,gBAAgB,CAAC,eAAe;QAClC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS,IAAM,iBAAiB;wBAChC,WAAU;kCACX;;;;;;;;;;;8BAIH,8OAAC;oBAAW,MAAM;;;;;;;;;;;;IAGxB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;mCAElC,MAAM,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAuC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9F,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;;;;;;iDAKH,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAEC,WAAU;oCACV,SAAS;wCACP,WAAW;wCACX,iBAAiB;oCACnB;8CAEA,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,iBAAiB,KAAK,EAAE;wDAC1B;wDACA,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;4CAK1E,KAAK,WAAW,kBACf,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,QAAQ;;;;;;0EAE3C,8OAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,8OAAC;;oEAAM,KAAK,MAAM,EAAE,UAAU,KAAK,MAAM,EAAE,UAAU;oEAAE;;;;;;;;;;;;;kEAEzD,8OAAC;kEAAM,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;mCAxC/B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAkDxB,8OAAC;gBACC,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,SAAS,MAAM,EAAE;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/components/groups/GroupDetail.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useGroups } from '@/contexts/GroupContext'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { MessageProvider } from '@/contexts/MessageContext'\nimport { NotesProvider } from '@/contexts/NotesContext'\nimport ChatInterface from '@/components/messages/ChatInterface'\nimport NotesInterface from '@/components/notes/NotesInterface'\n\nexport default function GroupDetail() {\n  const { selectedGroup, addMember } = useGroups()\n  const { user } = useAuth()\n  const [showAddMember, setShowAddMember] = useState(false)\n  const [memberEmail, setMemberEmail] = useState('')\n  const [memberRole, setMemberRole] = useState<'MEMBER' | 'ADMIN'>('MEMBER')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [activeTab, setActiveTab] = useState<'chat' | 'members' | 'notes'>('chat')\n\n  if (!selectedGroup) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n          </svg>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Select a Group</h3>\n          <p className=\"text-gray-500\">Choose a group from the sidebar to view details and start chatting</p>\n        </div>\n      </div>\n    )\n  }\n\n  const isOwnerOrAdmin = selectedGroup.ownerId === user?.id || \n    selectedGroup.members.some(m => m.userId === user?.id && ['OWNER', 'ADMIN'].includes(m.role))\n\n  const handleAddMember = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await addMember(selectedGroup.id, memberEmail, memberRole)\n      setMemberEmail('')\n      setMemberRole('MEMBER')\n      setShowAddMember(false)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to add member')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <MessageProvider>\n      <NotesProvider>\n        <div className=\"flex-1 flex flex-col bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <div className=\"flex items-center\">\n                <h1 className=\"text-xl font-semibold text-gray-900\">{selectedGroup.name}</h1>\n                {selectedGroup.isPrivate && (\n                  <svg className=\"w-4 h-4 text-gray-400 ml-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                )}\n              </div>\n              {selectedGroup.description && (\n                <p className=\"text-sm text-gray-600 mt-1\">{selectedGroup.description}</p>\n              )}\n            </div>\n            {isOwnerOrAdmin && (\n              <button\n                onClick={() => setShowAddMember(true)}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm\"\n              >\n                Add Member\n              </button>\n            )}\n          </div>\n\n          {/* Tabs */}\n          <div className=\"mt-4\">\n            <nav className=\"flex space-x-8\">\n              <button\n                onClick={() => setActiveTab('chat')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'chat'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Chat\n              </button>\n              <button\n                onClick={() => setActiveTab('members')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'members'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Members ({selectedGroup._count.members})\n              </button>\n              <button\n                onClick={() => setActiveTab('notes')}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'notes'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Notes ({selectedGroup._count.notes})\n              </button>\n            </nav>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 flex flex-col\">\n          {activeTab === 'chat' && (\n            <ChatInterface group={selectedGroup} />\n          )}\n\n          {activeTab === 'members' && (\n            <div className=\"flex-1 p-6 overflow-y-auto\">\n              <div className=\"max-w-4xl mx-auto\">\n                {/* Group Stats */}\n                <div className=\"grid grid-cols-3 gap-4 mb-8\">\n                  <div className=\"bg-white p-4 rounded-lg shadow\">\n                    <div className=\"text-2xl font-bold text-blue-600\">{selectedGroup._count.members}</div>\n                    <div className=\"text-sm text-gray-600\">Members</div>\n                  </div>\n                  <div className=\"bg-white p-4 rounded-lg shadow\">\n                    <div className=\"text-2xl font-bold text-green-600\">{selectedGroup._count.messages}</div>\n                    <div className=\"text-sm text-gray-600\">Messages</div>\n                  </div>\n                  <div className=\"bg-white p-4 rounded-lg shadow\">\n                    <div className=\"text-2xl font-bold text-purple-600\">{selectedGroup._count.notes}</div>\n                    <div className=\"text-sm text-gray-600\">Notes</div>\n                  </div>\n                </div>\n\n                {/* Members List */}\n                <div className=\"bg-white rounded-lg shadow\">\n                  <div className=\"p-4 border-b border-gray-200\">\n                    <h2 className=\"text-lg font-medium text-gray-900\">Members</h2>\n                  </div>\n                  <div className=\"p-4\">\n                    <div className=\"space-y-3\">\n                      {selectedGroup.members.map((member) => (\n                        <div key={member.id} className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-gray-700\">\n                                {(member.user.name || member.user.username).charAt(0).toUpperCase()}\n                              </span>\n                            </div>\n                            <div className=\"ml-3\">\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {member.user.name || member.user.username}\n                              </p>\n                              <p className=\"text-xs text-gray-500\">{member.user.email}</p>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                              member.role === 'OWNER' ? 'bg-green-100 text-green-800' :\n                              member.role === 'ADMIN' ? 'bg-blue-100 text-blue-800' :\n                              'bg-gray-100 text-gray-800'\n                            }`}>\n                              {member.role}\n                            </span>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'notes' && (\n            <NotesInterface group={selectedGroup} />\n          )}\n        </div>\n\n        {/* Add Member Modal */}\n        {showAddMember && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h2 className=\"text-xl font-bold text-gray-900\">Add Member</h2>\n                <button\n                  onClick={() => setShowAddMember(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n                  {error}\n                </div>\n              )}\n\n              <form onSubmit={handleAddMember}>\n                <div className=\"mb-4\">\n                  <label className=\"block text-gray-700 text-sm font-bold mb-2\">\n                    Email Address\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={memberEmail}\n                    onChange={(e) => setMemberEmail(e.target.value)}\n                    className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n                    placeholder=\"Enter user's email\"\n                    required\n                  />\n                </div>\n\n                <div className=\"mb-6\">\n                  <label className=\"block text-gray-700 text-sm font-bold mb-2\">\n                    Role\n                  </label>\n                  <select\n                    value={memberRole}\n                    onChange={(e) => setMemberRole(e.target.value as 'MEMBER' | 'ADMIN')}\n                    className=\"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n                  >\n                    <option value=\"MEMBER\">Member</option>\n                    <option value=\"ADMIN\">Admin</option>\n                  </select>\n                </div>\n\n                <div className=\"flex justify-end space-x-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAddMember(false)}\n                    className=\"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={loading}\n                    className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\"\n                  >\n                    {loading ? 'Adding...' : 'Add Member'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n        </div>\n      </NotesProvider>\n    </MessageProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,IAAA,6IAAS;IAC9C,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,0IAAO;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAqB;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAA+B;IAEzE,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC9F,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,iBAAiB,cAAc,OAAO,KAAK,MAAM,MACrD,cAAc,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,MAAM;YAAC;YAAS;SAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI;IAE7F,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,UAAU,cAAc,EAAE,EAAE,aAAa;YAC/C,eAAe;YACf,cAAc;YACd,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,qJAAe;kBACd,cAAA,8OAAC,iJAAa;sBACZ,cAAA,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC,cAAc,IAAI;;;;;;oDACtE,cAAc,SAAS,kBACtB,8OAAC;wDAAI,WAAU;wDAA6B,MAAK;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAyG,UAAS;;;;;;;;;;;;;;;;;4CAIlJ,cAAc,WAAW,kBACxB,8OAAC;gDAAE,WAAU;0DAA8B,cAAc,WAAW;;;;;;;;;;;;oCAGvE,gCACC,8OAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,kCACA,8EACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;;gDACH;gDACW,cAAc,MAAM,CAAC,OAAO;gDAAC;;;;;;;sDAEzC,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,kCACA,8EACJ;;gDACH;gDACS,cAAc,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,wBACb,8OAAC,0JAAa;gCAAC,OAAO;;;;;;4BAGvB,cAAc,2BACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAoC,cAAc,MAAM,CAAC,OAAO;;;;;;sEAC/E,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC,cAAc,MAAM,CAAC,QAAQ;;;;;;sEACjF,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAsC,cAAc,MAAM,CAAC,KAAK;;;;;;sEAC/E,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;8DAEpD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC1B,8OAAC;gEAAoB,WAAU;;kFAC7B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FACb,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0FAGrE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAE,WAAU;kGACV,OAAO,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ;;;;;;kGAE3C,8OAAC;wFAAE,WAAU;kGAAyB,OAAO,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;kFAG3D,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAW,CAAC,oEAAoE,EACpF,OAAO,IAAI,KAAK,UAAU,gCAC1B,OAAO,IAAI,KAAK,UAAU,8BAC1B,6BACA;sFACC,OAAO,IAAI;;;;;;;;;;;;+DApBR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAgChC,cAAc,yBACb,8OAAC,wJAAc;gCAAC,OAAO;;;;;;;;;;;;oBAK1B,+BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gCAK1E,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC;oCAAK,UAAU;;sDACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA6C;;;;;;8DAG9D,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA6C;;;;;;8DAG9D,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7C", "debugId": null}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { GroupProvider } from '@/contexts/GroupContext'\nimport GroupList from '@/components/groups/GroupList'\nimport GroupDetail from '@/components/groups/GroupDetail'\n\nexport default function DashboardPage() {\n  const { user, loading, logout } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to auth\n  }\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/auth')\n  }\n\n  return (\n    <GroupProvider>\n      <div className=\"min-h-screen bg-gray-50 flex flex-col\">\n        {/* Header */}\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center py-4\">\n              <div className=\"flex items-center\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">MyBinder</h1>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-sm text-gray-700\">\n                  Welcome, <span className=\"font-medium\">{user.name || user.username}</span>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm\"\n                >\n                  Logout\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex\">\n          <GroupList />\n          <GroupDetail />\n        </div>\n      </div>\n    </GroupProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,0IAAO;IACzC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wBAAwB;;IACtC;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC,iJAAa;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAwB;8DAC5B,8OAAC;oDAAK,WAAU;8DAAe,KAAK,IAAI,IAAI,KAAK,QAAQ;;;;;;;;;;;;sDAEpE,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oJAAS;;;;;sCACV,8OAAC,sJAAW;;;;;;;;;;;;;;;;;;;;;;AAKtB", "debugId": null}}]}