{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: any): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, JWT_SECRET)\n  } catch (error) {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAY;IACxC,OAAO,kJAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,kJAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,qBAAqB,OAAoB;IAC7D,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,eAAe;QACf,MAAM,UAAU,IAAA,mIAAW,EAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAA+D;IACzF,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,qBAAqB;QAExC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/api/groups/%5BgroupId%5D/notes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { getAuthenticatedUser } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst createNoteSchema = z.object({\n  title: z.string().min(1, 'Note title is required').max(200, 'Title too long'),\n  description: z.string().max(500, 'Description too long').optional(),\n})\n\n// GET /api/groups/[groupId]/notes - Get notes for a group\nexport async function GET(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { groupId } = await params\n\n    // Check if user is member of the group\n    const groupMember = await prisma.groupMember.findFirst({\n      where: {\n        groupId: groupId,\n        userId: user.id\n      }\n    })\n\n    if (!groupMember) {\n      return NextResponse.json(\n        { error: 'Access denied. You are not a member of this group.' },\n        { status: 403 }\n      )\n    }\n\n    // Get notes for the group\n    const notes = await prisma.note.findMany({\n      where: {\n        groupId: groupId\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        _count: {\n          select: {\n            blocks: true\n          }\n        }\n      },\n      orderBy: {\n        updatedAt: 'desc'\n      }\n    })\n\n    return NextResponse.json({ notes })\n  } catch (error) {\n    console.error('Get notes error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch notes' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST /api/groups/[groupId]/notes - Create a new note\nexport async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {\n  try {\n    const user = await getAuthenticatedUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })\n    }\n\n    const { groupId } = await params\n    const body = await request.json()\n    const { title, description } = createNoteSchema.parse(body)\n\n    // Check if user is member of the group\n    const groupMember = await prisma.groupMember.findFirst({\n      where: {\n        groupId: groupId,\n        userId: user.id\n      }\n    })\n\n    if (!groupMember) {\n      return NextResponse.json(\n        { error: 'Access denied. You are not a member of this group.' },\n        { status: 403 }\n      )\n    }\n\n    // Create note with initial empty text block\n    const note = await prisma.note.create({\n      data: {\n        title,\n        description,\n        authorId: user.id,\n        groupId: groupId,\n        blocks: {\n          create: {\n            type: 'TEXT',\n            content: '',\n            order: 0,\n            authorId: user.id\n          }\n        }\n      },\n      include: {\n        author: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        blocks: {\n          include: {\n            author: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          },\n          orderBy: {\n            order: 'asc'\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Note created successfully',\n      note\n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.issues },\n        { status: 400 }\n      )\n    }\n\n    console.error('Create note error:', error)\n    return NextResponse.json(\n      { error: 'Failed to create note' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB,oLAAC,CAAC,MAAM,CAAC;IAChC,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,KAAK;IAC5D,aAAa,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;AACnE;AAGO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAA4C;IAClG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAE1B,uCAAuC;QACvC,MAAM,cAAc,MAAM,gIAAM,CAAC,WAAW,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,SAAS;gBACT,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,QAAQ,MAAM,gIAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,OAAO;gBACL,SAAS;YACX;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB,EAAE,EAAE,MAAM,EAA4C;IACnG,IAAI;QACF,MAAM,OAAO,MAAM,IAAA,kJAAoB,EAAC;QACxC,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAC1B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,iBAAiB,KAAK,CAAC;QAEtD,uCAAuC;QACvC,MAAM,cAAc,MAAM,gIAAM,CAAC,WAAW,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,SAAS;gBACT,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqD,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,4CAA4C;QAC5C,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU,KAAK,EAAE;gBACjB,SAAS;gBACT,QAAQ;oBACN,QAAQ;wBACN,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,UAAU,KAAK,EAAE;oBACnB;gBACF;YACF;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,SAAS;wBACP,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}