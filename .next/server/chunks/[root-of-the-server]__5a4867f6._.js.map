{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret'\n\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\nexport function generateToken(payload: any): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\nexport function verifyToken(token: string): any {\n  try {\n    return jwt.verify(token, JWT_SECRET)\n  } catch (error) {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,OAAY;IACxC,OAAO,kJAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,kJAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/lib/middleware.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { verifyToken } from './auth'\nimport { prisma } from './prisma'\n\nexport async function getAuthenticatedUser(request: NextRequest) {\n  try {\n    // Get token from cookie or Authorization header\n    const token = request.cookies.get('auth-token')?.value || \n                  request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!token) {\n      return null\n    }\n\n    // Verify token\n    const payload = verifyToken(token)\n    if (!payload) {\n      return null\n    }\n\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: payload.userId },\n      select: {\n        id: true,\n        email: true,\n        username: true,\n        name: true,\n        avatar: true,\n        createdAt: true,\n        updatedAt: true,\n      },\n    })\n\n    return user\n  } catch (error) {\n    console.error('Auth middleware error:', error)\n    return null\n  }\n}\n\nexport function requireAuth(handler: (request: NextRequest, user: any) => Promise<Response>) {\n  return async (request: NextRequest) => {\n    const user = await getAuthenticatedUser(request)\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        { \n          status: 401,\n          headers: { 'Content-Type': 'application/json' }\n        }\n      )\n    }\n\n    return handler(request, user)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,qBAAqB,OAAoB;IAC7D,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAEvE,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,eAAe;QACf,MAAM,UAAU,IAAA,mIAAW,EAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAA+D;IACzF,OAAO,OAAO;QACZ,MAAM,OAAO,MAAM,qBAAqB;QAExC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/mybinder/src/app/api/groups/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { requireAuth } from '@/lib/middleware'\nimport { z } from 'zod'\n\nconst createGroupSchema = z.object({\n  name: z.string().min(1, 'Group name is required').max(100, 'Group name too long'),\n  description: z.string().max(500, 'Description too long').optional(),\n  isPrivate: z.boolean().optional().default(false),\n})\n\n// GET /api/groups - Get user's groups\nexport const GET = requireAuth(async (request: NextRequest, user: any) => {\n  try {\n    const groups = await prisma.group.findMany({\n      where: {\n        OR: [\n          { ownerId: user.id },\n          { \n            members: {\n              some: { userId: user.id }\n            }\n          }\n        ]\n      },\n      include: {\n        owner: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        members: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          }\n        },\n        _count: {\n          select: {\n            members: true,\n            messages: true,\n            notes: true,\n          }\n        }\n      },\n      orderBy: {\n        updatedAt: 'desc'\n      }\n    })\n\n    return NextResponse.json({ groups })\n  } catch (error) {\n    console.error('Get groups error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch groups' },\n      { status: 500 }\n    )\n  }\n})\n\n// POST /api/groups - Create new group\nexport const POST = requireAuth(async (request: NextRequest, user: any) => {\n  try {\n    const body = await request.json()\n    const { name, description, isPrivate } = createGroupSchema.parse(body)\n\n    const group = await prisma.group.create({\n      data: {\n        name,\n        description,\n        isPrivate,\n        ownerId: user.id,\n        members: {\n          create: {\n            userId: user.id,\n            role: 'OWNER'\n          }\n        }\n      },\n      include: {\n        owner: {\n          select: {\n            id: true,\n            username: true,\n            name: true,\n            avatar: true,\n          }\n        },\n        members: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                username: true,\n                name: true,\n                avatar: true,\n              }\n            }\n          }\n        },\n        _count: {\n          select: {\n            members: true,\n            messages: true,\n            notes: true,\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({ \n      message: 'Group created successfully',\n      group \n    })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.issues },\n        { status: 400 }\n      )\n    }\n\n    console.error('Create group error:', error)\n    return NextResponse.json(\n      { error: 'Failed to create group' },\n      { status: 500 }\n    )\n  }\n})\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,oBAAoB,oLAAC,CAAC,MAAM,CAAC;IACjC,MAAM,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,KAAK;IAC3D,aAAa,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;IACjE,WAAW,oLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;AAC5C;AAGO,MAAM,MAAM,IAAA,yIAAW,EAAC,OAAO,SAAsB;IAC1D,IAAI;QACF,MAAM,SAAS,MAAM,gIAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC,OAAO;gBACL,IAAI;oBACF;wBAAE,SAAS,KAAK,EAAE;oBAAC;oBACnB;wBACE,SAAS;4BACP,MAAM;gCAAE,QAAQ,KAAK,EAAE;4BAAC;wBAC1B;oBACF;iBACD;YACH;YACA,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,SAAS;wBACT,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE;QAAO;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,MAAM,OAAO,IAAA,yIAAW,EAAC,OAAO,SAAsB;IAC3D,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,kBAAkB,KAAK,CAAC;QAEjE,MAAM,QAAQ,MAAM,gIAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ;gBACA;gBACA;gBACA,SAAS,KAAK,EAAE;gBAChB,SAAS;oBACP,QAAQ;wBACN,QAAQ,KAAK,EAAE;wBACf,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,UAAU;gCACV,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,SAAS;wBACT,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oLAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}