"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/index.js":
/*!****************************************!*\
  !*** ./node_modules/bcryptjs/index.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   compareSync: () => (/* binding */ compareSync),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64),\n/* harmony export */   genSalt: () => (/* binding */ genSalt),\n/* harmony export */   genSaltSync: () => (/* binding */ genSaltSync),\n/* harmony export */   getRounds: () => (/* binding */ getRounds),\n/* harmony export */   getSalt: () => (/* binding */ getSalt),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   hashSync: () => (/* binding */ hashSync),\n/* harmony export */   setRandomFallback: () => (/* binding */ setRandomFallback),\n/* harmony export */   truncates: () => (/* binding */ truncates)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/*\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\n Copyright (c) 2012 Shane Girish <<EMAIL>>\n Copyright (c) 2025 Daniel Wirtz <<EMAIL>>\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions\n are met:\n 1. Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n 2. Redistributions in binary form must reproduce the above copyright\n notice, this list of conditions and the following disclaimer in the\n documentation and/or other materials provided with the distribution.\n 3. The name of the author may not be used to endorse or promote products\n derived from this software without specific prior written permission.\n\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n// The Node.js crypto module is used as a fallback for the Web Crypto API. When\n// building for the browser, inclusion of the crypto module should be disabled,\n// which the package hints at in its package.json for bundlers that support it.\n\n\n/**\n * The random implementation to use as a fallback.\n * @type {?function(number):!Array.<number>}\n * @inner\n */\nvar randomFallback = null;\n\n/**\n * Generates cryptographically secure random bytes.\n * @function\n * @param {number} len Bytes length\n * @returns {!Array.<number>} Random bytes\n * @throws {Error} If no random implementation is available\n * @inner\n */\nfunction randomBytes(len) {\n  // Web Crypto API. Globally available in the browser and in Node.js >=23.\n  try {\n    return crypto.getRandomValues(new Uint8Array(len));\n  } catch {}\n  // Node.js crypto module for non-browser environments.\n  try {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(len);\n  } catch {}\n  // Custom fallback specified with `setRandomFallback`.\n  if (!randomFallback) {\n    throw Error(\n      \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n    );\n  }\n  return randomFallback(len);\n}\n\n/**\n * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n *  is seeded properly!\n * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n * @see http://nodejs.org/api/crypto.html\n * @see http://www.w3.org/TR/WebCryptoAPI/\n */\nfunction setRandomFallback(random) {\n  randomFallback = random;\n}\n\n/**\n * Synchronously generates a salt.\n * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {number=} seed_length Not supported.\n * @returns {string} Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nfunction genSaltSync(rounds, seed_length) {\n  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof rounds !== \"number\")\n    throw Error(\n      \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n    );\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n  var salt = [];\n  salt.push(\"$2b$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n  return salt.join(\"\");\n}\n\n/**\n * Asynchronously generates a salt.\n * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {(number|function(Error, string=))=} seed_length Not supported.\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction genSalt(rounds, seed_length, callback) {\n  if (typeof seed_length === \"function\")\n    (callback = seed_length), (seed_length = undefined); // Not supported.\n  if (typeof rounds === \"function\") (callback = rounds), (rounds = undefined);\n  if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n  else if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  function _async(callback) {\n    nextTick(function () {\n      // Pretty thin, but salting is fast enough\n      try {\n        callback(null, genSaltSync(rounds));\n      } catch (err) {\n        callback(err);\n      }\n    });\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Synchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n * @returns {string} Resulting hash\n */\nfunction hashSync(password, salt) {\n  if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof password !== \"string\" || typeof salt !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n  return _hash(password, salt);\n}\n\n/**\n * Asynchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {number|string} salt Salt length to generate or salt to use\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction hash(password, salt, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password === \"string\" && typeof salt === \"number\")\n      genSalt(salt, function (err, salt) {\n        _hash(password, salt, callback, progressCallback);\n      });\n    else if (typeof password === \"string\" && typeof salt === \"string\")\n      _hash(password, salt, callback, progressCallback);\n    else\n      nextTick(\n        callback.bind(\n          this,\n          Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt),\n        ),\n      );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Compares two strings of the same length in constant time.\n * @param {string} known Must be of the correct length\n * @param {string} unknown Must be the same length as `known`\n * @returns {boolean}\n * @inner\n */\nfunction safeStringCompare(known, unknown) {\n  var diff = known.length ^ unknown.length;\n  for (var i = 0; i < known.length; ++i) {\n    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n  }\n  return diff === 0;\n}\n\n/**\n * Synchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hash Hash to test against\n * @returns {boolean} true if matching, otherwise false\n * @throws {Error} If an argument is illegal\n */\nfunction compareSync(password, hash) {\n  if (typeof password !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n  return safeStringCompare(\n    hashSync(password, hash.substring(0, hash.length - 31)),\n    hash,\n  );\n}\n\n/**\n * Asynchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hashValue Hash to test against\n * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction compare(password, hashValue, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n      nextTick(\n        callback.bind(\n          this,\n          Error(\n            \"Illegal arguments: \" + typeof password + \", \" + typeof hashValue,\n          ),\n        ),\n      );\n      return;\n    }\n    if (hashValue.length !== 60) {\n      nextTick(callback.bind(this, null, false));\n      return;\n    }\n    hash(\n      password,\n      hashValue.substring(0, 29),\n      function (err, comp) {\n        if (err) callback(err);\n        else callback(null, safeStringCompare(comp, hashValue));\n      },\n      progressCallback,\n    );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Gets the number of rounds used to encrypt the specified hash.\n * @param {string} hash Hash to extract the used number of rounds from\n * @returns {number} Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nfunction getRounds(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  return parseInt(hash.split(\"$\")[2], 10);\n}\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n * @param {string} hash Hash to extract the salt from\n * @returns {string} Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nfunction getSalt(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  if (hash.length !== 60)\n    throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n  return hash.substring(0, 29);\n}\n\n/**\n * Tests if a password will be truncated when hashed, that is its length is\n * greater than 72 bytes when converted to UTF-8.\n * @param {string} password The password to test\n * @returns {boolean} `true` if truncated, otherwise `false`\n */\nfunction truncates(password) {\n  if (typeof password !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password);\n  return utf8Length(password) > 72;\n}\n\n/**\n * Continues with the callback on the next tick.\n * @function\n * @param {function(...[*])} callback Callback to execute\n * @inner\n */\nvar nextTick =\n  typeof process !== \"undefined\" &&\n  process &&\n  typeof process.nextTick === \"function\"\n    ? typeof setImmediate === \"function\"\n      ? setImmediate\n      : process.nextTick\n    : setTimeout;\n\n/** Calculates the byte length of a string encoded as UTF8. */\nfunction utf8Length(string) {\n  var len = 0,\n    c = 0;\n  for (var i = 0; i < string.length; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128) len += 1;\n    else if (c < 2048) len += 2;\n    else if (\n      (c & 0xfc00) === 0xd800 &&\n      (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      ++i;\n      len += 4;\n    } else len += 3;\n  }\n  return len;\n}\n\n/** Converts a string to an array of UTF8 bytes. */\nfunction utf8Array(string) {\n  var offset = 0,\n    c1,\n    c2;\n  var buffer = new Array(utf8Length(string));\n  for (var i = 0, k = string.length; i < k; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = (c1 >> 6) | 192;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else if (\n      (c1 & 0xfc00) === 0xd800 &&\n      ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n    ) {\n      c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n      ++i;\n      buffer[offset++] = (c1 >> 18) | 240;\n      buffer[offset++] = ((c1 >> 12) & 63) | 128;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else {\n      buffer[offset++] = (c1 >> 12) | 224;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    }\n  }\n  return buffer;\n}\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n/**\n * bcrypt's own non-standard base64 dictionary.\n * @type {!Array.<string>}\n * @const\n * @inner\n **/\nvar BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\n/**\n * @type {!Array.<number>}\n * @const\n * @inner\n **/\nvar BASE64_INDEX = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,\n  -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28,\n  29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\n  48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n];\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input.\n * @param {!Array.<number>} b Byte array\n * @param {number} len Maximum input length\n * @returns {string}\n * @inner\n */\nfunction base64_encode(b, len) {\n  var off = 0,\n    rs = [],\n    c1,\n    c2;\n  if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n  while (off < len) {\n    c1 = b[off++] & 0xff;\n    rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    rs.push(BASE64_CODE[c2 & 0x3f]);\n  }\n  return rs.join(\"\");\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output.\n * @param {string} s String to decode\n * @param {number} len Maximum output length\n * @returns {!Array.<number>}\n * @inner\n */\nfunction base64_decode(s, len) {\n  var off = 0,\n    slen = s.length,\n    olen = 0,\n    rs = [],\n    c1,\n    c2,\n    c3,\n    c4,\n    o,\n    code;\n  if (len <= 0) throw Error(\"Illegal len: \" + len);\n  while (off < slen - 1 && olen < len) {\n    code = s.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = s.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c1 == -1 || c2 == -1) break;\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    rs.push(String.fromCharCode(o));\n    ++olen;\n  }\n  var res = [];\n  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n  return res;\n}\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BCRYPT_SALT_LEN = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BLOWFISH_NUM_ROUNDS = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar MAX_EXECUTION_TIME = 100;\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n\n/**\n * @param {Array.<number>} lr\n * @param {number} off\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @returns {Array.<number>}\n * @inner\n */\nfunction _encipher(lr, off, P, S) {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  var n,\n    l = lr[off],\n    r = lr[off + 1];\n\n  l ^= P[0];\n\n  /*\n    for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n        // Feistel substitution on left word\n        n  = S[l >>> 24],\n        n += S[0x100 | ((l >> 16) & 0xff)],\n        n ^= S[0x200 | ((l >> 8) & 0xff)],\n        n += S[0x300 | (l & 0xff)],\n        r ^= n ^ P[++i],\n        // Feistel substitution on right word\n        n  = S[r >>> 24],\n        n += S[0x100 | ((r >> 16) & 0xff)],\n        n ^= S[0x200 | ((r >> 8) & 0xff)],\n        n += S[0x300 | (r & 0xff)],\n        l ^= n ^ P[++i];\n    */\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n  return lr;\n}\n\n/**\n * @param {Array.<number>} data\n * @param {number} offp\n * @returns {{key: number, offp: number}}\n * @inner\n */\nfunction _streamtoword(data, offp) {\n  for (var i = 0, word = 0; i < 4; ++i)\n    (word = (word << 8) | (data[offp] & 0xff)),\n      (offp = (offp + 1) % data.length);\n  return { key: word, offp: offp };\n}\n\n/**\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _key(key, P, S) {\n  var offset = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offset)),\n      (offset = sw.offp),\n      (P[i] = P[i] ^ sw.key);\n  for (i = 0; i < plen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n}\n\n/**\n * Expensive key schedule Blowfish.\n * @param {Array.<number>} data\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _ekskey(data, key, P, S) {\n  var offp = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offp)), (offp = sw.offp), (P[i] = P[i] ^ sw.key);\n  offp = 0;\n  for (i = 0; i < plen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (P[i] = lr[0]),\n      (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (S[i] = lr[0]),\n      (S[i + 1] = lr[1]);\n}\n\n/**\n * Internaly crypts a string.\n * @param {Array.<number>} b Bytes to crypt\n * @param {Array.<number>} salt Salt bytes to use\n * @param {number} rounds Number of rounds\n * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n *  omitted, the operation will be performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _crypt(b, salt, rounds, callback, progressCallback) {\n  var cdata = C_ORIG.slice(),\n    clen = cdata.length,\n    err;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    err = Error(\n      \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n    );\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  rounds = (1 << rounds) >>> 0;\n\n  var P,\n    S,\n    i = 0,\n    j;\n\n  //Use typed arrays when available - huge speedup!\n  if (typeof Int32Array === \"function\") {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  _ekskey(salt, b, P, S);\n\n  /**\n   * Calcualtes the next round.\n   * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n   * @inner\n   */\n  function next() {\n    if (progressCallback) progressCallback(i / rounds);\n    if (i < rounds) {\n      var start = Date.now();\n      for (; i < rounds; ) {\n        i = i + 1;\n        _key(b, P, S);\n        _key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n      var ret = [];\n      for (i = 0; i < clen; i++)\n        ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n          ret.push((cdata[i] & 0xff) >>> 0);\n      if (callback) {\n        callback(null, ret);\n        return;\n      } else return ret;\n    }\n    if (callback) nextTick(next);\n  }\n\n  // Async\n  if (typeof callback !== \"undefined\") {\n    next();\n\n    // Sync\n  } else {\n    var res;\n    while (true) if (typeof (res = next()) !== \"undefined\") return res || [];\n  }\n}\n\n/**\n * Internally hashes a password.\n * @param {string} password Password to hash\n * @param {?string} salt Salt to use, actually never null\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n *  hashing is performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _hash(password, salt, callback, progressCallback) {\n  var err;\n  if (typeof password !== \"string\" || typeof salt !== \"string\") {\n    err = Error(\"Invalid string / salt: Not a string\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n\n  // Validate the salt\n  var minor, offset;\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.charAt(2) === \"$\") (minor = String.fromCharCode(0)), (offset = 3);\n  else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n      if (callback) {\n        nextTick(callback.bind(this, err));\n        return;\n      } else throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    err = Error(\"Missing salt rounds\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    real_salt = salt.substring(offset + 3, offset + 25);\n  password += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  var passwordb = utf8Array(password),\n    saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param {Array.<number>} bytes Byte array\n   * @returns {string}\n   * @inner\n   */\n  function finish(bytes) {\n    var res = [];\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(base64_encode(saltb, saltb.length));\n    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n    return res.join(\"\");\n  }\n\n  // Sync\n  if (typeof callback == \"undefined\")\n    return finish(_crypt(passwordb, saltb, rounds));\n  // Async\n  else {\n    _crypt(\n      passwordb,\n      saltb,\n      rounds,\n      function (err, bytes) {\n        if (err) callback(err, null);\n        else callback(null, finish(bytes));\n      },\n      progressCallback,\n    );\n  }\n}\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n * @function\n * @param {!Array.<number>} bytes Byte array\n * @param {number} length Maximum input length\n * @returns {string}\n */\nfunction encodeBase64(bytes, length) {\n  return base64_encode(bytes, length);\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n * @function\n * @param {string} string String to decode\n * @param {number} length Maximum output length\n * @returns {!Array.<number>}\n */\nfunction decodeBase64(string, length) {\n  return base64_decode(string, length);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  setRandomFallback,\n  genSaltSync,\n  genSalt,\n  hashSync,\n  hash,\n  compareSync,\n  compare,\n  getRounds,\n  getSalt,\n  truncates,\n  encodeBase64,\n  decodeBase64,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/index.js\n");

/***/ })

};
;