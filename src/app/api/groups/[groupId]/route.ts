import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const updateGroupSchema = z.object({
  name: z.string().min(1, 'Group name is required').max(100, 'Group name too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  isPrivate: z.boolean().optional(),
})

// GET /api/groups/[groupId] - Get specific group
export async function GET(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = params

    const group = await prisma.group.findFirst({
      where: {
        id: groupId,
        OR: [
          { ownerId: user.id },
          {
            members: {
              some: { userId: user.id }
            }
          }
        ]
      },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          },
          orderBy: {
            joinedAt: 'asc'
          }
        },
        _count: {
          select: {
            members: true,
            messages: true,
            notes: true,
          }
        }
      }
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({ group })
  } catch (error) {
    console.error('Get group error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch group' },
      { status: 500 }
    )
  }
}

// PUT /api/groups/[groupId] - Update group
export async function PUT(request: NextRequest, { params }: { params: { groupId: string } }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = params
    const body = await request.json()
    const updateData = updateGroupSchema.parse(body)

    // Check if user is owner or admin
    const group = await prisma.group.findFirst({
      where: {
        id: groupId,
        OR: [
          { ownerId: user.id },
          {
            members: {
              some: {
                userId: user.id,
                role: { in: ['OWNER', 'ADMIN'] }
              }
            }
          }
        ]
      }
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found or insufficient permissions' },
        { status: 404 }
      )
    }

    const updatedGroup = await prisma.group.update({
      where: { id: groupId },
      data: updateData,
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          }
        },
        _count: {
          select: {
            members: true,
            messages: true,
            notes: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Group updated successfully',
      group: updatedGroup
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Update group error:', error)
    return NextResponse.json(
      { error: 'Failed to update group' },
      { status: 500 }
    )
  }
}

// DELETE /api/groups/[groupId] - Delete group
export async function DELETE(request: NextRequest, { params }: { params: { groupId: string } }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = params

    // Check if user is owner
    const group = await prisma.group.findFirst({
      where: {
        id: groupId,
        ownerId: user.id
      }
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found or insufficient permissions' },
        { status: 404 }
      )
    }

    await prisma.group.delete({
      where: { id: groupId }
    })

    return NextResponse.json({
      message: 'Group deleted successfully'
    })
  } catch (error) {
    console.error('Delete group error:', error)
    return NextResponse.json(
      { error: 'Failed to delete group' },
      { status: 500 }
    )
  }
}
