# Next.js Architecture Documentation

## 🎯 **Why-How-What Framework**

### **Why**: Alasan <PERSON> Next.js 15 dengan App Router

#### **Business Requirements**
- **Real-time Group Chat**: Membutuhkan SSR untuk SEO dan performance
- **Note-taking Integration**: Perlu dynamic routing untuk notes management
- **User Authentication**: Memerlukan middleware dan API routes yang robust
- **Scalability**: Aplikasi harus dapat menangani multiple groups dan users

#### **Technical Justifications**
1. **App Router (Next.js 13+)**: Memberikan file-based routing yang lebih intuitif
2. **Server Components**: Mengurangi JavaScript bundle size di client
3. **Built-in API Routes**: Eliminasi kebutuhan backend terpisah
4. **Middleware Support**: Authentication dan authorization yang seamless
5. **TypeScript Integration**: Type safety untuk development yang lebih robust

### **How**: Implementasi Detail dan Struktur

#### **1. App Router Structure**
```
src/app/
├── (auth)/                 # Route groups untuk authentication
│   └── auth/
│       └── page.tsx       # Login/Register page
├── dashboard/             # Protected dashboard area
│   └── page.tsx          # Main dashboard
├── api/                   # API routes
│   ├── auth/             # Authentication endpoints
│   ├── groups/           # Group management
│   ├── notes/            # Notes management
│   └── blocks/           # Note blocks management
├── globals.css           # Global styles
├── layout.tsx            # Root layout
└── page.tsx              # Landing page
```

**Why this structure?**
- **Route Groups `(auth)`**: Mengorganisir routes tanpa mempengaruhi URL structure
- **Nested Layouts**: Shared UI components dan authentication logic
- **API Co-location**: API routes dekat dengan feature yang menggunakannya

#### **2. Dynamic API Routes Implementation**

**File**: `src/app/api/groups/[groupId]/messages/route.ts`
```typescript
export async function GET(
  request: NextRequest, 
  { params }: { params: Promise<{ groupId: string }> }
) {
  const { groupId } = await params  // Next.js 15 requirement
  // Implementation...
}
```

**Why await params?**
- **Next.js 15 Requirement**: Params sekarang async untuk better performance
- **Type Safety**: Promise<T> memberikan better TypeScript support
- **Future-proofing**: Persiapan untuk streaming dan concurrent features

#### **3. Server Components vs Client Components Strategy**

**Server Components** (Default):
- `src/app/dashboard/page.tsx` - Static content dan initial data
- `src/components/groups/GroupList.tsx` - Server-side data fetching
- Layout components yang tidak memerlukan interactivity

**Client Components** (`'use client'`):
- `src/components/messages/ChatInterface.tsx` - Real-time messaging
- `src/components/notes/NoteEditor.tsx` - Rich text editing
- Form components dengan state management

**Decision Matrix**:
```
Feature                 | Server | Client | Reason
------------------------|--------|--------|------------------
Initial Data Loading    |   ✓    |        | Better performance
Real-time Updates       |        |   ✓    | State management
Form Interactions       |        |   ✓    | User interactions
Static Content          |   ✓    |        | SEO optimization
```

#### **4. Middleware Implementation**

**File**: `src/lib/middleware.ts`
```typescript
export async function getAuthenticatedUser(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value
  if (!token) return null
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!)
    return await prisma.user.findUnique({ where: { id: decoded.userId } })
  } catch {
    return null
  }
}
```

**Why this approach?**
- **Cookie-based Auth**: More secure than localStorage
- **JWT Verification**: Stateless authentication
- **Database Lookup**: Fresh user data untuk setiap request

### **What**: Hasil dan Impact pada Aplikasi

#### **1. Performance Metrics**
- **First Load JS**: 121 kB (optimized dengan Server Components)
- **Build Time**: ~8 seconds (Turbopack acceleration)
- **Route Compilation**: Dynamic compilation untuk development speed

#### **2. Developer Experience**
- **Type Safety**: 100% TypeScript coverage
- **Hot Reload**: Instant feedback dengan Turbopack
- **API Co-location**: Reduced context switching
- **File-based Routing**: Intuitive navigation structure

#### **3. Production Benefits**
- **SEO Optimization**: Server-side rendering untuk public pages
- **Bundle Optimization**: Automatic code splitting
- **Caching Strategy**: Built-in caching untuk static assets
- **Edge Deployment**: Ready untuk Vercel Edge Functions

#### **4. Scalability Considerations**
- **Horizontal Scaling**: Stateless API design
- **Database Optimization**: Prisma query optimization
- **CDN Integration**: Static asset distribution
- **Monitoring**: Built-in analytics support

## 🔧 **Technical Decisions & Trade-offs**

### **App Router vs Pages Router**
**Chosen**: App Router
**Why**: 
- Better TypeScript integration
- Improved developer experience
- Future-proof architecture
- Better performance dengan Server Components

**Trade-off**: Learning curve untuk team familiar dengan Pages Router

### **Server Components Strategy**
**Chosen**: Hybrid approach (Server + Client Components)
**Why**:
- Optimal performance untuk static content
- Reduced JavaScript bundle
- Better SEO

**Trade-off**: Complexity dalam component boundary decisions

### **Authentication Strategy**
**Chosen**: JWT dengan HTTP-only cookies
**Why**:
- Secure token storage
- Stateless authentication
- Cross-domain support

**Trade-off**: Tidak bisa access token dari JavaScript (by design untuk security)

## 🚀 **Future Enhancements**

1. **Streaming**: Implement React 18 Suspense untuk better UX
2. **Edge Functions**: Migrate critical API routes ke Edge Runtime
3. **ISR**: Implement Incremental Static Regeneration untuk notes
4. **PWA**: Add Progressive Web App capabilities
5. **Micro-frontends**: Modular architecture untuk team scaling

## 📊 **Monitoring & Analytics**

- **Core Web Vitals**: Tracking performance metrics
- **Error Boundaries**: Graceful error handling
- **Logging**: Structured logging untuk debugging
- **Performance Monitoring**: Real-time performance tracking
