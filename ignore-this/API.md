# MyBinder API Documentation

Dokumentasi lengkap untuk REST API MyBinder.

## 🔐 Authentication

Semua endpoint (kecuali login/register) memerlukan JWT token dalam cookie `auth-token`.

### Headers
```
Content-Type: application/json
Cookie: auth-token=<jwt-token>
```

## 📝 Response Format

### Success Response
```json
{
  "data": {...},
  "message": "Success message"
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": [...] // Optional validation errors
}
```

## 🔑 Authentication Endpoints

### POST `/api/auth/register`
Registrasi pengguna baru.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "name": "User Name"
}
```

**Response (201):**
```json
{
  "message": "User created successfully",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "name": "User Name",
    "avatar": null,
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "token": "jwt-token"
}
```

**Errors:**
- `400` - Validation error
- `409` - Email/username already exists

### POST `/api/auth/login`
Login pengguna.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200):**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "name": "User Name",
    "avatar": null,
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "token": "jwt-token"
}
```

**Errors:**
- `400` - Validation error
- `401` - Invalid credentials

### POST `/api/auth/logout`
Logout pengguna (clear cookie).

**Response (200):**
```json
{
  "message": "Logged out successfully"
}
```

## 👥 Group Management

### GET `/api/groups`
Mendapatkan daftar grup pengguna.

**Response (200):**
```json
{
  "groups": [
    {
      "id": "group-id",
      "name": "Group Name",
      "description": "Group Description",
      "ownerId": "owner-id",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "memberCount": 5,
      "role": "OWNER" // OWNER, ADMIN, MEMBER
    }
  ]
}
```

### POST `/api/groups`
Membuat grup baru.

**Request Body:**
```json
{
  "name": "Group Name",
  "description": "Group Description"
}
```

**Response (201):**
```json
{
  "message": "Group created successfully",
  "group": {
    "id": "group-id",
    "name": "Group Name",
    "description": "Group Description",
    "ownerId": "user-id",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### GET `/api/groups/[groupId]`
Mendapatkan detail grup dan pesan.

**Response (200):**
```json
{
  "group": {
    "id": "group-id",
    "name": "Group Name",
    "description": "Group Description",
    "ownerId": "owner-id",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "members": [
      {
        "id": "member-id",
        "userId": "user-id",
        "role": "MEMBER",
        "user": {
          "id": "user-id",
          "username": "username",
          "name": "User Name",
          "avatar": null
        }
      }
    ]
  },
  "messages": [
    {
      "id": "message-id",
      "content": "Message content",
      "authorId": "author-id",
      "groupId": "group-id",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "author": {
        "id": "author-id",
        "username": "username",
        "name": "Author Name",
        "avatar": null
      }
    }
  ]
}
```

### POST `/api/groups/[groupId]/messages`
Mengirim pesan ke grup.

**Request Body:**
```json
{
  "content": "Message content"
}
```

**Response (201):**
```json
{
  "message": "Message sent successfully",
  "data": {
    "id": "message-id",
    "content": "Message content",
    "authorId": "author-id",
    "groupId": "group-id",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### POST `/api/groups/[groupId]/join`
Bergabung dengan grup.

**Response (200):**
```json
{
  "message": "Joined group successfully"
}
```

### DELETE `/api/groups/[groupId]/leave`
Keluar dari grup.

**Response (200):**
```json
{
  "message": "Left group successfully"
}
```

## 📝 Notes Management

### GET `/api/groups/[groupId]/notes`
Mendapatkan daftar catatan grup.

**Response (200):**
```json
{
  "notes": [
    {
      "id": "note-id",
      "title": "Note Title",
      "description": "Note Description",
      "authorId": "author-id",
      "groupId": "group-id",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "author": {
        "id": "author-id",
        "username": "username",
        "name": "Author Name"
      }
    }
  ]
}
```

### POST `/api/groups/[groupId]/notes`
Membuat catatan baru.

**Request Body:**
```json
{
  "title": "Note Title",
  "description": "Note Description",
  "blocks": [
    {
      "type": "TEXT",
      "content": "Block content",
      "order": 0
    }
  ]
}
```

**Response (201):**
```json
{
  "message": "Note created successfully",
  "note": {
    "id": "note-id",
    "title": "Note Title",
    "description": "Note Description",
    "authorId": "author-id",
    "groupId": "group-id",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### GET `/api/notes/[noteId]`
Mendapatkan detail catatan dengan blok.

**Response (200):**
```json
{
  "note": {
    "id": "note-id",
    "title": "Note Title",
    "description": "Note Description",
    "authorId": "author-id",
    "groupId": "group-id",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "blocks": [
      {
        "id": "block-id",
        "type": "TEXT",
        "content": "Block content",
        "order": 0,
        "noteId": "note-id",
        "authorId": "author-id",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

### PUT `/api/notes/[noteId]`
Update catatan.

**Request Body:**
```json
{
  "title": "Updated Title",
  "description": "Updated Description"
}
```

### DELETE `/api/notes/[noteId]`
Hapus catatan.

**Response (200):**
```json
{
  "message": "Note deleted successfully"
}
```

## 🧩 Note Blocks

### POST `/api/notes/[noteId]/blocks`
Tambah blok baru ke catatan.

**Request Body:**
```json
{
  "type": "TEXT", // TEXT, HEADING_1, HEADING_2, BULLET_LIST, NUMBERED_LIST, CODE
  "content": "Block content",
  "order": 0
}
```

### PUT `/api/blocks/[blockId]`
Update blok.

**Request Body:**
```json
{
  "content": "Updated content",
  "order": 1
}
```

### DELETE `/api/blocks/[blockId]`
Hapus blok.

## 📊 Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `500` - Internal Server Error

## 🔍 Error Examples

### Validation Error (400)
```json
{
  "error": "Validation error",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

### Unauthorized (401)
```json
{
  "error": "Unauthorized access"
}
```

### Not Found (404)
```json
{
  "error": "Group not found"
}
```

---

**API Documentation v1.0**
